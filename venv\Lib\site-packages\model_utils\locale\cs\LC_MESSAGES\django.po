# Czech translations of django-model-utils
#
# This file is distributed under the same license as the django-model-utils package.
#
# Translators:
# ------------
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018.
#
msgid ""
msgstr ""
"Project-Id-Version: django-model-utils\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-04-01 15:01+0200\n"
"PO-Revision-Date: 2018-05-04 13:46+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: N/A\n"
"Language: cs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1) ? 0 : (n>=2 && n<=4) ? 1 : 2;\n"
"X-Generator: Poedit 2.0.7\n"

#: models.py:24
msgid "created"
msgstr "vytvořeno"

#: models.py:25
msgid "modified"
msgstr "upraveno"

#: models.py:49
msgid "start"
msgstr "začátek"

#: models.py:50
msgid "end"
msgstr "konec"

#: models.py:65
msgid "status"
msgstr "stav"

#: models.py:66
msgid "status changed"
msgstr "změna stavu"
