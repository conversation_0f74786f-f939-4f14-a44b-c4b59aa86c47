from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib import messages
from django.views.generic import List<PERSON>iew, DetailView, C<PERSON>View
from django.urls import reverse_lazy
from django.http import JsonResponse
from django.contrib.auth import get_user_model
from .models import Conversation, Message, Notification
from .forms import MessageForm, NotificationForm

User = get_user_model()

class ConversationListView(LoginRequiredMixin, ListView):
    model = Conversation
    template_name = 'messaging/conversation_list.html'
    context_object_name = 'conversations'
    paginate_by = 20

    def get_queryset(self):
        return Conversation.objects.filter(
            participants=self.request.user
        ).order_by('-updated_at')

class ConversationDetailView(LoginRequiredMixin, DetailView):
    model = Conversation
    template_name = 'messaging/conversation_detail.html'
    context_object_name = 'conversation'

    def get_queryset(self):
        return Conversation.objects.filter(participants=self.request.user)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['messages'] = self.object.messages.order_by('created_at')
        context['message_form'] = MessageForm()

        # Mark messages as read
        self.object.messages.filter(is_read=False).exclude(
            sender=self.request.user
        ).update(is_read=True)

        return context

@login_required
def start_conversation(request):
    """Start a new conversation with admin"""
    if request.user.is_admin:
        messages.error(request, 'Admins cannot start conversations with themselves.')
        return redirect('messaging:conversations')

    # Find or create conversation with admin
    admin_user = User.objects.filter(is_superuser=True).first()
    if not admin_user:
        admin_user = User.objects.filter(user_type='admin').first()

    if not admin_user:
        messages.error(request, 'No admin available for messaging.')
        return redirect('messaging:conversations')

    conversation = Conversation.objects.filter(
        participants=request.user
    ).filter(participants=admin_user).first()

    if not conversation:
        conversation = Conversation.objects.create(
            subject=f'Support Request from {request.user.username}'
        )
        conversation.participants.add(request.user, admin_user)

    return redirect('messaging:conversation_detail', conversation.pk)

@login_required
def send_message(request, conversation_id):
    conversation = get_object_or_404(
        Conversation,
        id=conversation_id,
        participants=request.user
    )

    if request.method == 'POST':
        form = MessageForm(request.POST, request.FILES)
        if form.is_valid():
            message = form.save(commit=False)
            message.conversation = conversation
            message.sender = request.user
            message.save()

            # Update conversation timestamp
            conversation.save()

            # Create notification for other participants
            other_participants = conversation.participants.exclude(id=request.user.id)
            for participant in other_participants:
                Notification.objects.create(
                    user=participant,
                    notification_type='new_message',
                    title='New Message',
                    message=f'You have a new message from {request.user.username}'
                )

            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': True,
                    'message': {
                        'id': message.id,
                        'content': message.content,
                        'sender': message.sender.username,
                        'created_at': message.created_at.strftime('%Y-%m-%d %H:%M:%S')
                    }
                })

            return redirect('messaging:conversation_detail', conversation.id)

    return redirect('messaging:conversation_detail', conversation.id)

class NotificationListView(LoginRequiredMixin, ListView):
    model = Notification
    template_name = 'messaging/notification_list.html'
    context_object_name = 'notifications'
    paginate_by = 20

    def get_queryset(self):
        return Notification.objects.filter(user=self.request.user).order_by('-created_at')

@login_required
def mark_notification_read(request, notification_id):
    notification = get_object_or_404(Notification, id=notification_id, user=request.user)
    notification.is_read = True
    notification.save()

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({'success': True})

    return redirect('messaging:notifications')

@login_required
def mark_all_notifications_read(request):
    Notification.objects.filter(user=request.user, is_read=False).update(is_read=True)
    messages.success(request, 'All notifications marked as read.')
    return redirect('messaging:notifications')

# Admin Views
class AdminConversationListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    model = Conversation
    template_name = 'messaging/admin_conversations.html'
    context_object_name = 'conversations'
    paginate_by = 20

    def test_func(self):
        return self.request.user.is_admin

    def get_queryset(self):
        return Conversation.objects.all().order_by('-updated_at')

@login_required
def send_notification(request):
    if not request.user.is_admin:
        messages.error(request, 'Access denied.')
        return redirect('accounts:dashboard')

    if request.method == 'POST':
        form = NotificationForm(request.POST)
        if form.is_valid():
            notification_data = form.cleaned_data
            users = notification_data['users']

            for user in users:
                Notification.objects.create(
                    user=user,
                    notification_type='system',
                    title=notification_data['title'],
                    message=notification_data['message']
                )

            messages.success(request, f'Notification sent to {users.count()} user(s).')
            return redirect('messaging:admin_conversations')
    else:
        form = NotificationForm()

    return render(request, 'messaging/send_notification.html', {'form': form})
