from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal

User = get_user_model()

class Room(models.Model):
    ROOM_TYPE_CHOICES = (
        ('single', 'Single Room'),
        ('double', 'Double Room'),
        ('suite', 'Suite'),
        ('family', 'Family Room'),
    )

    name = models.CharField(max_length=100)
    room_type = models.CharField(max_length=20, choices=ROOM_TYPE_CHOICES)
    capacity = models.PositiveIntegerField()
    price_per_night = models.DecimalField(max_digits=10, decimal_places=2)
    description = models.TextField()
    amenities = models.TextField(help_text="List amenities separated by commas")
    is_available = models.BooleanField(default=True)
    room_image = models.ImageField(upload_to='room_images/', blank=True, null=True)
    created_at = models.DateTime<PERSON>ield(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.get_room_type_display()}"

    def is_available_for_dates(self, check_in, check_out):
        """Check if room is available for given date range"""
        overlapping_bookings = self.booking_set.filter(
            status__in=['confirmed', 'pending'],
            check_in_date__lt=check_out,
            check_out_date__gt=check_in
        )
        return not overlapping_bookings.exists()

    class Meta:
        ordering = ['name']


class Booking(models.Model):
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    room = models.ForeignKey(Room, on_delete=models.CASCADE)
    check_in_date = models.DateField()
    check_out_date = models.DateField()
    guests_count = models.PositiveIntegerField()
    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    special_requests = models.TextField(blank=True, null=True)
    booking_reference = models.CharField(max_length=20, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    confirmed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='confirmed_bookings'
    )
    confirmed_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Booking {self.booking_reference} - {self.user.username}"

    def save(self, *args, **kwargs):
        if not self.booking_reference:
            import uuid
            self.booking_reference = str(uuid.uuid4())[:8].upper()

        # Calculate total price
        if self.check_in_date and self.check_out_date and self.room:
            nights = (self.check_out_date - self.check_in_date).days
            self.total_price = self.room.price_per_night * nights

        super().save(*args, **kwargs)

    def get_duration_nights(self):
        return (self.check_out_date - self.check_in_date).days

    def can_be_cancelled(self):
        """Check if booking can be cancelled (at least 24 hours before check-in)"""
        if self.status in ['cancelled', 'completed', 'rejected']:
            return False
        return self.check_in_date > timezone.now().date()

    def can_be_modified(self):
        """Check if booking can be modified"""
        return self.status == 'pending' and self.check_in_date > timezone.now().date()

    class Meta:
        ordering = ['-created_at']
