{% extends 'base.html' %}

{% block title %}Profile - Carthage Hill Guest House{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-user-edit"></i> My Profile</h4>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name</label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger">{{ form.first_name.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name</label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger">{{ form.last_name.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">Email</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger">{{ form.email.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.phone_number.id_for_label }}" class="form-label">Phone Number</label>
                                {{ form.phone_number }}
                                {% if form.phone_number.errors %}
                                    <div class="text-danger">{{ form.phone_number.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.address.id_for_label }}" class="form-label">Address</label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="text-danger">{{ form.address.errors }}</div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.date_of_birth.id_for_label }}" class="form-label">Date of Birth</label>
                                {{ form.date_of_birth }}
                                {% if form.date_of_birth.errors %}
                                    <div class="text-danger">{{ form.date_of_birth.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.profile_picture.id_for_label }}" class="form-label">Profile Picture</label>
                                {{ form.profile_picture }}
                                {% if form.profile_picture.errors %}
                                    <div class="text-danger">{{ form.profile_picture.errors }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save"></i> Update Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Account Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Username:</strong> {{ user.username }}</p>
                        <p><strong>Account Type:</strong> {{ user.get_user_type_display }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Member Since:</strong> {{ user.date_joined|date:"F d, Y" }}</p>
                        <p><strong>Last Updated:</strong> {{ user.updated_at|date:"F d, Y" }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
