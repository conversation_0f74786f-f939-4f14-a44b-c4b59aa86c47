django_notifications_hq-1.8.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
django_notifications_hq-1.8.3.dist-info/METADATA,sha256=efNZmqcqruGuMWPhY77S30UxOIG8xojStJPqoNv49Ck,18729
django_notifications_hq-1.8.3.dist-info/RECORD,,
django_notifications_hq-1.8.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
django_notifications_hq-1.8.3.dist-info/WHEEL,sha256=JNWh1Fm1UdwIQV075glCn4MVuCRs0sotJIq-J6rbxCU,109
django_notifications_hq-1.8.3.dist-info/licenses/AUTHORS.txt,sha256=u2dElVLrfCnKIWjQfgbFm0_rrz08RnAFzcZQgQvK6gw,1076
django_notifications_hq-1.8.3.dist-info/licenses/LICENSE.txt,sha256=IS5QlauPs4pURAl-uuwJXWDHI3opaZLZGARTu3zLvA0,1563
django_notifications_hq-1.8.3.dist-info/top_level.txt,sha256=1ZMEmhh8poJCymhkp_f0OtRKgu_e0paddCjPCllV4fw,14
notifications/__init__.py,sha256=Qd_SK6GUXSOCPFgw8C18T1-2A2eWcyepVRhZrW1a_qA,406
notifications/__pycache__/__init__.cpython-313.pyc,,
notifications/__pycache__/admin.cpython-313.pyc,,
notifications/__pycache__/apps.cpython-313.pyc,,
notifications/__pycache__/helpers.cpython-313.pyc,,
notifications/__pycache__/models.cpython-313.pyc,,
notifications/__pycache__/settings.cpython-313.pyc,,
notifications/__pycache__/signals.cpython-313.pyc,,
notifications/__pycache__/urls.cpython-313.pyc,,
notifications/__pycache__/utils.cpython-313.pyc,,
notifications/__pycache__/views.cpython-313.pyc,,
notifications/admin.py,sha256=ozZsPxp9rfc93cAwNyDM3oTA_6Sy-ocWPnCyyc2bnKU,1038
notifications/apps.py,sha256=OpFHKwUMCsfEa1qivK2cV3rR76aLPlrzoMdhZPsW5RM,499
notifications/base/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
notifications/base/__pycache__/__init__.cpython-313.pyc,,
notifications/base/__pycache__/admin.cpython-313.pyc,,
notifications/base/__pycache__/models.cpython-313.pyc,,
notifications/base/admin.py,sha256=HpbIB4UnzeK8uKkZMQp0cWWhxsTyBiGBF-qHfdg75hY,440
notifications/base/models.py,sha256=AJyay5Im7-wrDto2FyZYSFEnaEKYkId07DT6FttkxOo,13483
notifications/helpers.py,sha256=dgnsZ8Ys-_W6iDQ-V4DnkxVWaSSCFodY-CFnE6tDfAA,1416
notifications/locale/ru/LC_MESSAGES/django.po,sha256=bN7lxtTS5bpNqxsX1nTXBDei1nM10oienHU5IOMFkZ4,3489
notifications/migrations/0001_initial.py,sha256=mGMw109qcQQGmoAg9h6veIUI_Lc6w-sTl-sTSkk1HNE,2201
notifications/migrations/0002_auto_20150224_1134.py,sha256=TimwrXrwYie31RsRtS-ZtN-9zyVkXFl0VPoHhkRHwQk,595
notifications/migrations/0003_notification_data.py,sha256=EUJNPJYifdCxRx-rrY3pDh1T-5Nwi64xImbl5rr1kfI,446
notifications/migrations/0004_auto_20150826_1508.py,sha256=q49W3yzhHT2v0vt6d2g1nssBbrlP5SEf6jBPUDCBwHM,420
notifications/migrations/0005_auto_20160504_1520.py,sha256=NvwYcR05tpu72Qmal4-ImzbGhNXq0KB7LvMf6fNz57Q,532
notifications/migrations/0006_indexes.py,sha256=WtV8vInassgUtQ4je24H8lMFPIcB0_fuB2dpblkS8nA,947
notifications/migrations/0007_add_timestamp_index.py,sha256=9bOQ7J7NutZZiGHj-PY4nrsrvKpbEvIWdpLyDPvnKqg,458
notifications/migrations/0008_index_together_recipient_unread.py,sha256=Y8bH0cQcBJvBb3DkLn_LKay1PlvC6nYtut5a3b9vOUc,469
notifications/migrations/0009_alter_notification_options_and_more.py,sha256=N6NBdQvbCKXuvPS8nP1pUJZQF_BHxlMn_1lAIrMJbVo,5448
notifications/migrations/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
notifications/migrations/__pycache__/0001_initial.cpython-313.pyc,,
notifications/migrations/__pycache__/0002_auto_20150224_1134.cpython-313.pyc,,
notifications/migrations/__pycache__/0003_notification_data.cpython-313.pyc,,
notifications/migrations/__pycache__/0004_auto_20150826_1508.cpython-313.pyc,,
notifications/migrations/__pycache__/0005_auto_20160504_1520.cpython-313.pyc,,
notifications/migrations/__pycache__/0006_indexes.cpython-313.pyc,,
notifications/migrations/__pycache__/0007_add_timestamp_index.cpython-313.pyc,,
notifications/migrations/__pycache__/0008_index_together_recipient_unread.cpython-313.pyc,,
notifications/migrations/__pycache__/0009_alter_notification_options_and_more.cpython-313.pyc,,
notifications/migrations/__pycache__/__init__.cpython-313.pyc,,
notifications/models.py,sha256=XDbL-ELU1GWo9Damza6qUVFi7LbpubBE7DYuDBki-6w,826
notifications/settings.py,sha256=2hmmDB6bsobg0ggHJudLCjuiALs-cTpMlNjUphXMcc4,427
notifications/signals.py,sha256=Xv3bt_6dnrea9rgpu23zlAQwo7l70X136ztebEJctso,119
notifications/static/notifications/notify.js,sha256=Q6aoQycGcvdFKUhAXH3b9Dgef7FUyVk0clgI0E7JWFw,2869
notifications/templates/notifications/list.html,sha256=ZjAEyFOotYT20rG0h0FxTnuqhM561AMLpOKrYa8ZHKc,130
notifications/templates/notifications/notice.html,sha256=epHlFhc20GZD6VbdkvfV8nqqYepn4cpy6RlsYrENbbU,655
notifications/templates/notifications/test_tags.html,sha256=xECeZVRmyO_Q_8dZyaB4jsdtW-rTp-hz3iyTof4FL4E,220
notifications/templatetags/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
notifications/templatetags/__pycache__/__init__.cpython-313.pyc,,
notifications/templatetags/__pycache__/notifications_tags.cpython-313.pyc,,
notifications/templatetags/notifications_tags.py,sha256=FOp_2ohKVC1BioTfN3NILmPHPh-63fdyTkTR_lzbPiI,4193
notifications/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
notifications/tests/__pycache__/__init__.cpython-313.pyc,,
notifications/tests/__pycache__/settings.cpython-313.pyc,,
notifications/tests/__pycache__/tests.cpython-313.pyc,,
notifications/tests/__pycache__/urls.cpython-313.pyc,,
notifications/tests/__pycache__/views.cpython-313.pyc,,
notifications/tests/sample_notifications/__init__.py,sha256=9fKqLH75jaJXPbiQy5DmxokDEfPz7mc7TInuxmuM9gQ,95
notifications/tests/sample_notifications/__pycache__/__init__.cpython-313.pyc,,
notifications/tests/sample_notifications/__pycache__/admin.cpython-313.pyc,,
notifications/tests/sample_notifications/__pycache__/apps.cpython-313.pyc,,
notifications/tests/sample_notifications/__pycache__/models.cpython-313.pyc,,
notifications/tests/sample_notifications/__pycache__/tests.cpython-313.pyc,,
notifications/tests/sample_notifications/admin.py,sha256=8CUKYo08bIdroOcEjiAzLlX6MZhAilpTNXqFnvr3_oA,272
notifications/tests/sample_notifications/apps.py,sha256=kc_DQPQT9dRqMnE7XpWqnk_Z9EzlDJkDuMiVVZUzh2A,204
notifications/tests/sample_notifications/migrations/0001_initial.py,sha256=CrB1t6U86o1fcEKEiBbcwOpG_aBUaJcenUtwPL3gQGE,2720
notifications/tests/sample_notifications/migrations/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
notifications/tests/sample_notifications/migrations/__pycache__/0001_initial.cpython-313.pyc,,
notifications/tests/sample_notifications/migrations/__pycache__/__init__.cpython-313.pyc,,
notifications/tests/sample_notifications/models.py,sha256=BAi4GGf2LV-WU8u2CLaugp78JLnY6tG8QAbOQAPXJ_4,270
notifications/tests/sample_notifications/templatetags/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
notifications/tests/sample_notifications/templatetags/__pycache__/__init__.cpython-313.pyc,,
notifications/tests/sample_notifications/templatetags/__pycache__/notifications_tags.cpython-313.pyc,,
notifications/tests/sample_notifications/templatetags/notifications_tags.py,sha256=8xWB7Wsq-O83SuYoJPtxzUGcWQfmsbOnIpcPk8foD3Q,67
notifications/tests/sample_notifications/tests.py,sha256=FzcWTb-MfPcMa6vM5eRSeYgCAJgcHxTfzOMxuo0Q6Do,1791
notifications/tests/settings.py,sha256=zOsroypXL66DT5mk6ilUI04Gb1d4324gTq4DfzcQOl0,2128
notifications/tests/templates/test_live.html,sha256=jtSS45nftV-9EWUAkviERxt4ccm-qn6nkilritflo94,507
notifications/tests/tests.py,sha256=Zk4QzJ_JcYHLa1235aMryJWHB8i7eEm8Vy_7DDQWc-0,28778
notifications/tests/urls.py,sha256=ZyJ0-HVfFmLhx-4UTAEuqL6MWTWG8dD0fgbthZ6HbIM,1821
notifications/tests/views.py,sha256=eBLgZZ_fwoCdF3-H4PElpYLPDPoDlB3lRHINmOjZcPk,909
notifications/urls.py,sha256=9JzISK1odJy-xc-9GYpTy5sVSc6jtvInVgeE5jE329E,1338
notifications/utils.py,sha256=IALsVscIhw5ScozbfYuMkmDvbmTg5mIbGYat8QCoD_s,267
notifications/views.py,sha256=EO0-l3srg6vFY3IFfNfhnO4fvgq90MBD7OCr18h3qn8,6142
