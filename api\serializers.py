from rest_framework import serializers
from django.contrib.auth import get_user_model
from bookings.models import Room, Booking
from messaging.models import Conversation, Message, Notification
from reviews.models import Review

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 
                 'phone_number', 'user_type', 'profile_picture', 'date_joined']
        read_only_fields = ['id', 'username', 'user_type', 'date_joined']

class UserRegistrationSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, min_length=8)
    password_confirm = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = ['username', 'email', 'first_name', 'last_name', 
                 'phone_number', 'password', 'password_confirm']
    
    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match")
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.user_type = 'client'
        user.save()
        return user

class RoomSerializer(serializers.ModelSerializer):
    average_rating = serializers.SerializerMethodField()
    review_count = serializers.SerializerMethodField()
    amenities_list = serializers.SerializerMethodField()
    
    class Meta:
        model = Room
        fields = ['id', 'name', 'room_type', 'capacity', 'price_per_night',
                 'description', 'amenities', 'amenities_list', 'is_available',
                 'room_image', 'average_rating', 'review_count', 'created_at']
    
    def get_average_rating(self, obj):
        reviews = obj.reviews.filter(is_approved=True)
        if reviews.exists():
            return round(sum(r.overall_rating for r in reviews) / reviews.count(), 1)
        return 0
    
    def get_review_count(self, obj):
        return obj.reviews.filter(is_approved=True).count()
    
    def get_amenities_list(self, obj):
        if obj.amenities:
            return [amenity.strip() for amenity in obj.amenities.split(',')]
        return []

class BookingSerializer(serializers.ModelSerializer):
    room_details = RoomSerializer(source='room', read_only=True)
    user_details = UserSerializer(source='user', read_only=True)
    duration_nights = serializers.SerializerMethodField()
    can_cancel = serializers.SerializerMethodField()
    can_modify = serializers.SerializerMethodField()
    
    class Meta:
        model = Booking
        fields = ['id', 'booking_reference', 'room', 'room_details', 'user', 'user_details',
                 'check_in_date', 'check_out_date', 'guests_count', 'total_price',
                 'status', 'special_requests', 'duration_nights', 'can_cancel',
                 'can_modify', 'created_at', 'updated_at']
        read_only_fields = ['id', 'booking_reference', 'user', 'total_price', 
                           'created_at', 'updated_at']
    
    def get_duration_nights(self, obj):
        return obj.get_duration_nights()
    
    def get_can_cancel(self, obj):
        return obj.can_be_cancelled()
    
    def get_can_modify(self, obj):
        return obj.can_be_modified()
    
    def validate(self, attrs):
        room = attrs.get('room')
        check_in_date = attrs.get('check_in_date')
        check_out_date = attrs.get('check_out_date')
        guests_count = attrs.get('guests_count')
        
        if check_in_date and check_out_date:
            if check_out_date <= check_in_date:
                raise serializers.ValidationError("Check-out date must be after check-in date")
            
            if room and not room.is_available_for_dates(check_in_date, check_out_date):
                raise serializers.ValidationError("Room is not available for selected dates")
        
        if room and guests_count and guests_count > room.capacity:
            raise serializers.ValidationError(f"Room capacity is {room.capacity} guests")
        
        return attrs

class BookingCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Booking
        fields = ['room', 'check_in_date', 'check_out_date', 'guests_count', 'special_requests']
    
    def validate(self, attrs):
        room = attrs.get('room')
        check_in_date = attrs.get('check_in_date')
        check_out_date = attrs.get('check_out_date')
        guests_count = attrs.get('guests_count')
        
        if check_in_date and check_out_date:
            if check_out_date <= check_in_date:
                raise serializers.ValidationError("Check-out date must be after check-in date")
            
            if room and not room.is_available_for_dates(check_in_date, check_out_date):
                raise serializers.ValidationError("Room is not available for selected dates")
        
        if room and guests_count and guests_count > room.capacity:
            raise serializers.ValidationError(f"Room capacity is {room.capacity} guests")
        
        return attrs

class MessageSerializer(serializers.ModelSerializer):
    sender_details = UserSerializer(source='sender', read_only=True)
    
    class Meta:
        model = Message
        fields = ['id', 'content', 'sender', 'sender_details', 'is_read', 
                 'attachment', 'created_at']
        read_only_fields = ['id', 'sender', 'created_at']

class ConversationSerializer(serializers.ModelSerializer):
    participants_details = UserSerializer(source='participants', many=True, read_only=True)
    last_message = MessageSerializer(source='get_last_message', read_only=True)
    unread_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Conversation
        fields = ['id', 'subject', 'participants', 'participants_details',
                 'last_message', 'unread_count', 'created_at', 'updated_at']
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_unread_count(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return obj.messages.filter(is_read=False).exclude(sender=request.user).count()
        return 0

class NotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notification
        fields = ['id', 'notification_type', 'title', 'message', 'is_read',
                 'related_booking', 'created_at']
        read_only_fields = ['id', 'created_at']

class ReviewSerializer(serializers.ModelSerializer):
    user_details = UserSerializer(source='user', read_only=True)
    room_details = RoomSerializer(source='room', read_only=True)
    average_rating = serializers.SerializerMethodField()
    
    class Meta:
        model = Review
        fields = ['id', 'booking', 'user', 'user_details', 'room', 'room_details',
                 'overall_rating', 'cleanliness_rating', 'service_rating',
                 'location_rating', 'value_rating', 'average_rating',
                 'title', 'comment', 'would_recommend', 'is_approved', 'created_at']
        read_only_fields = ['id', 'user', 'room', 'is_approved', 'created_at']
    
    def get_average_rating(self, obj):
        return obj.get_average_rating()

class ReviewCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Review
        fields = ['booking', 'overall_rating', 'cleanliness_rating', 'service_rating',
                 'location_rating', 'value_rating', 'title', 'comment', 'would_recommend']
    
    def validate_booking(self, value):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            if value.user != request.user:
                raise serializers.ValidationError("You can only review your own bookings")
            if value.status != 'completed':
                raise serializers.ValidationError("You can only review completed bookings")
            if hasattr(value, 'review'):
                raise serializers.ValidationError("You have already reviewed this booking")
        return value
