from django import forms
from django.contrib.auth import get_user_model
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Submit, Field
from .models import Message, Notification

User = get_user_model()

class MessageForm(forms.ModelForm):
    class Meta:
        model = Message
        fields = ['content', 'attachment']
        widgets = {
            'content': forms.Textarea(attrs={
                'rows': 3,
                'placeholder': 'Type your message here...',
                'class': 'form-control'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_id = 'message-form'
        self.helper.layout = Layout(
            Field('content', css_class='mb-2'),
            Field('attachment', css_class='mb-2'),
            Submit('submit', 'Send Message', css_class='btn btn-primary')
        )

class NotificationForm(forms.Form):
    users = forms.ModelMultipleChoiceField(
        queryset=User.objects.filter(user_type='client'),
        widget=forms.CheckboxSelectMultiple,
        required=True,
        help_text="Select users to send notification to"
    )
    title = forms.CharField(
        max_length=200,
        widget=forms.TextInput(attrs={'placeholder': 'Notification title'})
    )
    message = forms.CharField(
        widget=forms.Textarea(attrs={
            'rows': 4,
            'placeholder': 'Notification message'
        })
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            'title',
            'message',
            'users',
            Submit('submit', 'Send Notification', css_class='btn btn-success')
        )
