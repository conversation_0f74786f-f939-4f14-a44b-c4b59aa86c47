from django import forms
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Submit, Row, Column, Field, HTML
from .models import Review

class ReviewForm(forms.ModelForm):
    class Meta:
        model = Review
        fields = [
            'title', 'comment', 'overall_rating', 'cleanliness_rating',
            'service_rating', 'location_rating', 'value_rating', 'would_recommend'
        ]
        widgets = {
            'title': forms.TextInput(attrs={'placeholder': 'Give your review a title'}),
            'comment': forms.Textarea(attrs={'rows': 4, 'placeholder': 'Share your experience...'}),
            'overall_rating': forms.Select(choices=[(i, f'{i} Star{"s" if i != 1 else ""}') for i in range(1, 6)]),
            'cleanliness_rating': forms.Select(choices=[(i, f'{i} Star{"s" if i != 1 else ""}') for i in range(1, 6)]),
            'service_rating': forms.Select(choices=[(i, f'{i} Star{"s" if i != 1 else ""}') for i in range(1, 6)]),
            'location_rating': forms.Select(choices=[(i, f'{i} Star{"s" if i != 1 else ""}') for i in range(1, 6)]),
            'value_rating': forms.Select(choices=[(i, f'{i} Star{"s" if i != 1 else ""}') for i in range(1, 6)]),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            'title',
            'comment',
            HTML('<h5 class="mt-4 mb-3">Rate Your Experience</h5>'),
            Row(
                Column('overall_rating', css_class='form-group col-md-6 mb-0'),
                Column('cleanliness_rating', css_class='form-group col-md-6 mb-0'),
                css_class='form-row'
            ),
            Row(
                Column('service_rating', css_class='form-group col-md-6 mb-0'),
                Column('location_rating', css_class='form-group col-md-6 mb-0'),
                css_class='form-row'
            ),
            Row(
                Column('value_rating', css_class='form-group col-md-6 mb-0'),
                Column('would_recommend', css_class='form-group col-md-6 mb-0'),
                css_class='form-row'
            ),
            Submit('submit', 'Submit Review', css_class='btn btn-success btn-lg')
        )

class ReviewFilterForm(forms.Form):
    RATING_CHOICES = [
        ('', 'All Ratings'),
        ('5', '5 Stars'),
        ('4', '4+ Stars'),
        ('3', '3+ Stars'),
        ('2', '2+ Stars'),
        ('1', '1+ Stars'),
    ]
    
    rating = forms.ChoiceField(
        choices=RATING_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    room_type = forms.ChoiceField(
        choices=[('', 'All Room Types')] + list(Review._meta.get_field('room').related_model.ROOM_TYPE_CHOICES),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'get'
        self.helper.layout = Layout(
            Row(
                Column('rating', css_class='form-group col-md-6 mb-0'),
                Column('room_type', css_class='form-group col-md-6 mb-0'),
                css_class='form-row'
            ),
            Submit('submit', 'Filter Reviews', css_class='btn btn-primary')
        )
