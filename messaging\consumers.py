import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from .models import Conversation, Message, Notification

User = get_user_model()

class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.conversation_id = self.scope['url_route']['kwargs']['conversation_id']
        self.room_group_name = f'chat_{self.conversation_id}'
        
        # Check if user is participant in conversation
        if await self.is_participant():
            # Join room group
            await self.channel_layer.group_add(
                self.room_group_name,
                self.channel_name
            )
            await self.accept()
        else:
            await self.close()
    
    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        text_data_json = json.loads(text_data)
        message_content = text_data_json['message']
        
        # Save message to database
        message = await self.save_message(message_content)
        
        if message:
            # Send message to room group
            await self.channel_layer.group_send(
                self.room_group_name,
                {
                    'type': 'chat_message',
                    'message': {
                        'id': message.id,
                        'content': message.content,
                        'sender': message.sender.username,
                        'sender_id': message.sender.id,
                        'created_at': message.created_at.isoformat()
                    }
                }
            )
    
    async def chat_message(self, event):
        message = event['message']
        
        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'chat_message',
            'message': message
        }))
    
    @database_sync_to_async
    def is_participant(self):
        try:
            conversation = Conversation.objects.get(id=self.conversation_id)
            return conversation.participants.filter(id=self.scope['user'].id).exists()
        except Conversation.DoesNotExist:
            return False
    
    @database_sync_to_async
    def save_message(self, content):
        try:
            conversation = Conversation.objects.get(id=self.conversation_id)
            if conversation.participants.filter(id=self.scope['user'].id).exists():
                message = Message.objects.create(
                    conversation=conversation,
                    sender=self.scope['user'],
                    content=content
                )
                
                # Update conversation timestamp
                conversation.save()
                
                # Create notifications for other participants
                other_participants = conversation.participants.exclude(id=self.scope['user'].id)
                for participant in other_participants:
                    Notification.objects.create(
                        user=participant,
                        notification_type='new_message',
                        title='New Message',
                        message=f'You have a new message from {self.scope["user"].username}'
                    )
                
                return message
        except Conversation.DoesNotExist:
            pass
        return None

class NotificationConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        if self.scope['user'].is_authenticated:
            self.user_group_name = f'notifications_{self.scope["user"].id}'
            
            # Join user notification group
            await self.channel_layer.group_add(
                self.user_group_name,
                self.channel_name
            )
            await self.accept()
        else:
            await self.close()
    
    async def disconnect(self, close_code):
        if hasattr(self, 'user_group_name'):
            # Leave user notification group
            await self.channel_layer.group_discard(
                self.user_group_name,
                self.channel_name
            )
    
    async def notification_message(self, event):
        notification = event['notification']
        
        # Send notification to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'notification',
            'notification': notification
        }))
