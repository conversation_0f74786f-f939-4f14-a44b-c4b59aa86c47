jsonfield-3.2.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
jsonfield-3.2.0.dist-info/METADATA,sha256=KffoHaNdBrB31Fa_JZyXd0SQIsijklSdT__lXXVPS4A,6612
jsonfield-3.2.0.dist-info/RECORD,,
jsonfield-3.2.0.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
jsonfield-3.2.0.dist-info/licenses/LICENSE,sha256=wk4ZJg0xZs72hZubk4LnUPCQu_YPU773yr3L2WsNSFw,1060
jsonfield-3.2.0.dist-info/top_level.txt,sha256=vKhrOliM1tJJBXUhSXSoHcWs_90pUa7ogHvn-mzxGKQ,10
jsonfield/__init__.py,sha256=6Qa-0efnJqzqBXTeSH5AFcqGP5vhdudb_7vqtLbuwng,88
jsonfield/__pycache__/__init__.cpython-313.pyc,,
jsonfield/__pycache__/encoder.cpython-313.pyc,,
jsonfield/__pycache__/fields.cpython-313.pyc,,
jsonfield/__pycache__/forms.cpython-313.pyc,,
jsonfield/__pycache__/json.cpython-313.pyc,,
jsonfield/encoder.py,sha256=sm4UtTAxr0AOeCrcyCbJm4h2h_KNYviI5--1CxlOzKM,2280
jsonfield/fields.py,sha256=hfh2SICVIVyaNWv5TuPc1ZFnaxEFZ6AV5gmI7HZAYiU,3753
jsonfield/forms.py,sha256=MuI-1Qdi8JR_dgw6fwq7GvTdnYPH-hlmXJoWyWvN6ug,2301
jsonfield/json.py,sha256=x-TpdHUehBKEjpbcq52CdiYqckQW9VjHAZcw0bWUGRY,535
