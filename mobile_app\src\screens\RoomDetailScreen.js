import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  Image,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Title,
  Paragraph,
  Button,
  Chip,
  ActivityIndicator,
  TextInput,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DatePicker from 'react-native-date-picker';
import { apiService } from '../services/apiService';
import { theme, styles } from '../theme/theme';

export default function RoomDetailScreen({ route, navigation }) {
  const { roomId } = route.params;
  const [room, setRoom] = useState(null);
  const [loading, setLoading] = useState(true);
  const [bookingLoading, setBookingLoading] = useState(false);
  const [checkInDate, setCheckInDate] = useState(new Date());
  const [checkOutDate, setCheckOutDate] = useState(new Date(Date.now() + 86400000));
  const [guestsCount, setGuestsCount] = useState('1');
  const [specialRequests, setSpecialRequests] = useState('');
  const [showCheckInPicker, setShowCheckInPicker] = useState(false);
  const [showCheckOutPicker, setShowCheckOutPicker] = useState(false);

  useEffect(() => {
    loadRoom();
  }, [roomId]);

  const loadRoom = async () => {
    try {
      const response = await apiService.getRoom(roomId);
      setRoom(response.data);
    } catch (error) {
      console.error('Error loading room:', error);
      Alert.alert('Error', 'Failed to load room details');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const calculateNights = () => {
    const diffTime = Math.abs(checkOutDate - checkInDate);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const calculateTotal = () => {
    if (!room) return 0;
    return room.price_per_night * calculateNights();
  };

  const handleBooking = async () => {
    if (!room) return;

    if (parseInt(guestsCount) > room.capacity) {
      Alert.alert('Error', `This room can accommodate maximum ${room.capacity} guests`);
      return;
    }

    if (checkOutDate <= checkInDate) {
      Alert.alert('Error', 'Check-out date must be after check-in date');
      return;
    }

    setBookingLoading(true);
    try {
      const bookingData = {
        room: room.id,
        check_in_date: checkInDate.toISOString().split('T')[0],
        check_out_date: checkOutDate.toISOString().split('T')[0],
        guests_count: parseInt(guestsCount),
        special_requests: specialRequests,
      };

      const response = await apiService.createBooking(bookingData);
      Alert.alert(
        'Booking Successful!',
        `Your booking has been submitted. Reference: ${response.data.booking_reference}`,
        [
          {
            text: 'OK',
            onPress: () => navigation.navigate('Bookings'),
          },
        ]
      );
    } catch (error) {
      console.error('Booking error:', error);
      const errorMessage = error.response?.data?.detail || 
                          error.response?.data?.error || 
                          'Failed to create booking';
      Alert.alert('Booking Failed', errorMessage);
    } finally {
      setBookingLoading(false);
    }
  };

  const renderStars = (rating) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Icon
          key={i}
          name="star"
          size={20}
          color={i <= rating ? '#FFD700' : '#E0E0E0'}
        />
      );
    }
    return stars;
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.center]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.text, { marginTop: 10 }]}>Loading room details...</Text>
      </View>
    );
  }

  if (!room) {
    return (
      <View style={[styles.container, styles.center]}>
        <Text style={styles.text}>Room not found</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Room Image */}
      {room.room_image ? (
        <Image
          source={{ uri: room.room_image }}
          style={{ height: 250, width: '100%' }}
          resizeMode="cover"
        />
      ) : (
        <View style={[styles.center, { height: 250, backgroundColor: theme.colors.accent }]}>
          <Icon name="hotel" size={80} color={theme.colors.primary} />
        </View>
      )}

      {/* Room Details */}
      <Card style={[styles.card, { margin: 15 }]}>
        <Card.Content>
          <Title style={{ fontSize: 24, marginBottom: 10 }}>{room.name}</Title>
          
          <View style={[styles.row, { marginBottom: 15 }]}>
            <Chip mode="outlined" style={{ marginRight: 10 }}>
              {room.room_type.replace('_', ' ').toUpperCase()}
            </Chip>
            <Chip mode="outlined" style={{ marginRight: 10 }}>
              {room.capacity} Guest{room.capacity !== 1 ? 's' : ''}
            </Chip>
            <Chip
              style={{
                backgroundColor: room.is_available ? theme.colors.success : theme.colors.error,
              }}
              textStyle={{ color: '#fff' }}>
              {room.is_available ? 'Available' : 'Unavailable'}
            </Chip>
          </View>

          <View style={[styles.row, { marginBottom: 15 }]}>
            <View style={styles.row}>
              {renderStars(Math.round(room.average_rating || 0))}
              <Text style={[styles.text, { marginLeft: 8 }]}>
                {room.average_rating ? room.average_rating.toFixed(1) : '0.0'} ({room.review_count || 0} reviews)
              </Text>
            </View>
          </View>

          <Paragraph style={{ marginBottom: 15, lineHeight: 22 }}>
            {room.description}
          </Paragraph>

          <Text style={[styles.subtitle, { marginBottom: 10 }]}>Amenities</Text>
          <View style={{ flexDirection: 'row', flexWrap: 'wrap', marginBottom: 15 }}>
            {room.amenities_list?.map((amenity, index) => (
              <Chip key={index} mode="outlined" style={{ margin: 2 }}>
                {amenity}
              </Chip>
            )) || <Text style={styles.smallText}>No amenities listed</Text>}
          </View>

          <View style={[styles.row, { marginBottom: 20 }]}>
            <Text style={[styles.title, { color: theme.colors.primary, fontSize: 28 }]}>
              ${room.price_per_night}
            </Text>
            <Text style={[styles.text, { marginLeft: 5 }]}>per night</Text>
          </View>
        </Card.Content>
      </Card>

      {/* Booking Form */}
      {room.is_available && (
        <Card style={[styles.card, { margin: 15 }]}>
          <Card.Content>
            <Title style={styles.subtitle}>Book This Room</Title>

            <View style={[styles.row, { marginVertical: 10 }]}>
              <Button
                mode="outlined"
                onPress={() => setShowCheckInPicker(true)}
                style={{ flex: 1, marginRight: 5 }}>
                Check-in: {checkInDate.toLocaleDateString()}
              </Button>
              <Button
                mode="outlined"
                onPress={() => setShowCheckOutPicker(true)}
                style={{ flex: 1, marginLeft: 5 }}>
                Check-out: {checkOutDate.toLocaleDateString()}
              </Button>
            </View>

            <TextInput
              label="Number of Guests"
              value={guestsCount}
              onChangeText={setGuestsCount}
              mode="outlined"
              keyboardType="numeric"
              style={styles.input}
            />

            <TextInput
              label="Special Requests (Optional)"
              value={specialRequests}
              onChangeText={setSpecialRequests}
              mode="outlined"
              multiline
              numberOfLines={3}
              style={styles.input}
            />

            <View style={[styles.row, { marginVertical: 15 }]}>
              <Text style={styles.text}>
                {calculateNights()} night{calculateNights() !== 1 ? 's' : ''} × ${room.price_per_night}
              </Text>
              <Text style={[styles.title, { color: theme.colors.primary }]}>
                Total: ${calculateTotal()}
              </Text>
            </View>

            <Button
              mode="contained"
              onPress={handleBooking}
              disabled={bookingLoading}
              style={styles.button}
              contentStyle={{ paddingVertical: 8 }}>
              {bookingLoading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                'Book Now'
              )}
            </Button>
          </Card.Content>
        </Card>
      )}

      {/* Date Pickers */}
      <DatePicker
        modal
        open={showCheckInPicker}
        date={checkInDate}
        mode="date"
        minimumDate={new Date()}
        onConfirm={(date) => {
          setShowCheckInPicker(false);
          setCheckInDate(date);
          if (date >= checkOutDate) {
            setCheckOutDate(new Date(date.getTime() + 86400000));
          }
        }}
        onCancel={() => setShowCheckInPicker(false)}
      />

      <DatePicker
        modal
        open={showCheckOutPicker}
        date={checkOutDate}
        mode="date"
        minimumDate={new Date(checkInDate.getTime() + 86400000)}
        onConfirm={(date) => {
          setShowCheckOutPicker(false);
          setCheckOutDate(date);
        }}
        onCancel={() => setShowCheckOutPicker(false)}
      />
    </ScrollView>
  );
}
