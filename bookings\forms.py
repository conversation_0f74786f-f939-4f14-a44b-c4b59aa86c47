from django import forms
from django.core.exceptions import ValidationError
from django.utils import timezone
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Submit, Row, Column, Field
from .models import Booking, Room

class BookingForm(forms.ModelForm):
    class Meta:
        model = Booking
        fields = ['room', 'check_in_date', 'check_out_date', 'guests_count', 'special_requests']
        widgets = {
            'check_in_date': forms.DateInput(attrs={'type': 'date'}),
            'check_out_date': forms.DateInput(attrs={'type': 'date'}),
            'special_requests': forms.Textarea(attrs={'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Filter available rooms
        self.fields['room'].queryset = Room.objects.filter(is_available=True)
        
        self.helper = FormHelper()
        self.helper.layout = Layout(
            'room',
            Row(
                Column('check_in_date', css_class='form-group col-md-6 mb-0'),
                Column('check_out_date', css_class='form-group col-md-6 mb-0'),
                css_class='form-row'
            ),
            'guests_count',
            'special_requests',
            Submit('submit', 'Book Now', css_class='btn btn-success btn-lg')
        )
    
    def clean(self):
        cleaned_data = super().clean()
        check_in_date = cleaned_data.get('check_in_date')
        check_out_date = cleaned_data.get('check_out_date')
        room = cleaned_data.get('room')
        guests_count = cleaned_data.get('guests_count')
        
        # Validate dates
        if check_in_date and check_out_date:
            if check_in_date <= timezone.now().date():
                raise ValidationError("Check-in date must be in the future.")
            
            if check_out_date <= check_in_date:
                raise ValidationError("Check-out date must be after check-in date.")
            
            # Check room availability
            if room and not room.is_available_for_dates(check_in_date, check_out_date):
                raise ValidationError("This room is not available for the selected dates.")
        
        # Validate guest count
        if room and guests_count:
            if guests_count > room.capacity:
                raise ValidationError(f"This room can accommodate maximum {room.capacity} guests.")
        
        return cleaned_data

class RoomSearchForm(forms.Form):
    check_in_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        required=False
    )
    check_out_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        required=False
    )
    guests_count = forms.IntegerField(
        min_value=1,
        max_value=10,
        required=False,
        widget=forms.NumberInput(attrs={'placeholder': 'Number of guests'})
    )
    room_type = forms.ChoiceField(
        choices=[('', 'Any Room Type')] + list(Room.ROOM_TYPE_CHOICES),
        required=False
    )
    max_price = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(attrs={'placeholder': 'Max price per night'})
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'get'
        self.helper.layout = Layout(
            Row(
                Column('check_in_date', css_class='form-group col-md-3 mb-0'),
                Column('check_out_date', css_class='form-group col-md-3 mb-0'),
                Column('guests_count', css_class='form-group col-md-2 mb-0'),
                Column('room_type', css_class='form-group col-md-2 mb-0'),
                Column('max_price', css_class='form-group col-md-2 mb-0'),
                css_class='form-row'
            ),
            Submit('submit', 'Search Rooms', css_class='btn btn-primary')
        )

class RoomForm(forms.ModelForm):
    class Meta:
        model = Room
        fields = ['name', 'room_type', 'capacity', 'price_per_night', 'description', 
                 'amenities', 'is_available', 'room_image']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 4}),
            'amenities': forms.Textarea(attrs={'rows': 3, 'placeholder': 'List amenities separated by commas'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.layout = Layout(
            Row(
                Column('name', css_class='form-group col-md-6 mb-0'),
                Column('room_type', css_class='form-group col-md-6 mb-0'),
                css_class='form-row'
            ),
            Row(
                Column('capacity', css_class='form-group col-md-6 mb-0'),
                Column('price_per_night', css_class='form-group col-md-6 mb-0'),
                css_class='form-row'
            ),
            'description',
            'amenities',
            Row(
                Column('is_available', css_class='form-group col-md-6 mb-0'),
                Column('room_image', css_class='form-group col-md-6 mb-0'),
                css_class='form-row'
            ),
            Submit('submit', 'Save Room', css_class='btn btn-success')
        )
