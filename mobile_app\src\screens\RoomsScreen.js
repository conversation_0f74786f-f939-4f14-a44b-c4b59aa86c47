import React, { useState, useEffect } from 'react';
import {
  View,
  FlatList,
  RefreshControl,
  TouchableOpacity,
  Image,
} from 'react-native';
import {
  Text,
  Card,
  Title,
  Paragraph,
  Chip,
  Button,
  Searchbar,
  ActivityIndicator,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { apiService } from '../services/apiService';
import { theme, styles } from '../theme/theme';

export default function RoomsScreen({ navigation }) {
  const [rooms, setRooms] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({});

  useEffect(() => {
    loadRooms();
  }, [filters]);

  const loadRooms = async () => {
    setLoading(true);
    try {
      const response = await apiService.getRooms(filters);
      setRooms(response.data.results || response.data || []);
    } catch (error) {
      console.error('Error loading rooms:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadRooms();
    setRefreshing(false);
  };

  const filteredRooms = rooms.filter(room =>
    room.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    room.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderStars = (rating) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Icon
          key={i}
          name="star"
          size={16}
          color={i <= rating ? '#FFD700' : '#E0E0E0'}
        />
      );
    }
    return stars;
  };

  const renderRoom = ({ item }) => (
    <TouchableOpacity
      onPress={() => navigation.navigate('RoomDetail', { roomId: item.id })}>
      <Card style={[styles.card, { marginHorizontal: 15, marginVertical: 8 }]}>
        {item.room_image ? (
          <Image
            source={{ uri: item.room_image }}
            style={{
              height: 200,
              borderTopLeftRadius: theme.roundness,
              borderTopRightRadius: theme.roundness,
            }}
            resizeMode="cover"
          />
        ) : (
          <View
            style={[
              styles.center,
              {
                height: 200,
                backgroundColor: theme.colors.accent,
                borderTopLeftRadius: theme.roundness,
                borderTopRightRadius: theme.roundness,
              },
            ]}>
            <Icon name="hotel" size={60} color={theme.colors.primary} />
          </View>
        )}

        <Card.Content style={{ padding: 15 }}>
          <View style={styles.row}>
            <View style={{ flex: 1 }}>
              <Title style={{ fontSize: 18, marginBottom: 5 }}>{item.name}</Title>
              <View style={[styles.row, { marginBottom: 8 }]}>
                <Chip
                  mode="outlined"
                  style={{ marginRight: 8 }}
                  textStyle={{ fontSize: 12 }}>
                  {item.room_type.replace('_', ' ').toUpperCase()}
                </Chip>
                <Chip
                  mode="outlined"
                  textStyle={{ fontSize: 12 }}>
                  {item.capacity} Guest{item.capacity !== 1 ? 's' : ''}
                </Chip>
              </View>
            </View>
            <View style={styles.center}>
              <Text style={[styles.title, { color: theme.colors.primary, fontSize: 20 }]}>
                ${item.price_per_night}
              </Text>
              <Text style={styles.smallText}>per night</Text>
            </View>
          </View>

          <Paragraph numberOfLines={2} style={{ marginBottom: 10 }}>
            {item.description}
          </Paragraph>

          <View style={[styles.row, { marginBottom: 10 }]}>
            <View style={styles.row}>
              {renderStars(Math.round(item.average_rating || 0))}
              <Text style={[styles.smallText, { marginLeft: 5 }]}>
                ({item.review_count || 0} reviews)
              </Text>
            </View>
            <Chip
              style={{
                backgroundColor: item.is_available
                  ? theme.colors.success
                  : theme.colors.error,
              }}
              textStyle={{ color: '#fff', fontSize: 12 }}>
              {item.is_available ? 'Available' : 'Unavailable'}
            </Chip>
          </View>

          <Button
            mode="contained"
            onPress={() => navigation.navigate('RoomDetail', { roomId: item.id })}
            style={{ marginTop: 10 }}
            disabled={!item.is_available}>
            View Details
          </Button>
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );

  if (loading && rooms.length === 0) {
    return (
      <View style={[styles.container, styles.center]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={[styles.text, { marginTop: 10 }]}>Loading rooms...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={{ padding: 15, backgroundColor: theme.colors.surface }}>
        <Searchbar
          placeholder="Search rooms..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={{ elevation: 2 }}
        />
      </View>

      <FlatList
        data={filteredRooms}
        renderItem={renderRoom}
        keyExtractor={(item) => item.id.toString()}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={[styles.center, { padding: 50 }]}>
            <Icon name="search-off" size={60} color={theme.colors.placeholder} />
            <Text style={[styles.text, { marginTop: 15, textAlign: 'center' }]}>
              No rooms found
            </Text>
            <Text style={[styles.smallText, { textAlign: 'center', marginTop: 5 }]}>
              Try adjusting your search criteria
            </Text>
          </View>
        }
        contentContainerStyle={{ paddingBottom: 20 }}
      />
    </View>
  );
}
