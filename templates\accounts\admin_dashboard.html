{% extends 'base.html' %}

{% block title %}Admin Dashboard - Carthage Hill Guest House{% endblock %}

{% block content %}
<div class="admin-dashboard-container">
    <div class="row">
        <div class="col-12">
            <div class="dashboard-header fade-in">
                <h2><i class="fas fa-cogs pulse-animation"></i> Admin Dashboard</h2>
                <p class="text-muted">Comprehensive system overview and management</p>
                <div class="dashboard-actions">
                    <a href="{% url 'admin:index' %}" class="btn btn-primary hover-btn">
                        <i class="fas fa-tools"></i> Django Admin
                    </a>
                    <a href="{% url 'bookings:admin_bookings' %}" class="btn btn-success hover-btn">
                        <i class="fas fa-calendar-check"></i> Manage Bookings
                    </a>
                    <a href="{% url 'messaging:admin_conversations' %}" class="btn btn-info hover-btn">
                        <i class="fas fa-comments"></i> Messages
                    </a>
                </div>
            </div>
        </div>
    </div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center bg-warning text-dark">
            <div class="card-body">
                <i class="fas fa-clock fa-3x mb-3"></i>
                <h3>{{ pending_bookings }}</h3>
                <p>Pending Bookings</p>
                <a href="{% url 'bookings:admin_bookings' %}?status=pending" class="btn btn-dark btn-sm">Review</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center bg-info text-white">
            <div class="card-body">
                <i class="fas fa-bed fa-3x mb-3"></i>
                <h3>{{ total_rooms }}</h3>
                <p>Total Rooms</p>
                <a href="{% url 'bookings:admin_rooms' %}" class="btn btn-light btn-sm">Manage</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center bg-success text-white">
            <div class="card-body">
                <i class="fas fa-star fa-3x mb-3"></i>
                <h3>{{ recent_reviews }}</h3>
                <p>Pending Reviews</p>
                <a href="{% url 'reviews:admin_reviews' %}" class="btn btn-light btn-sm">Approve</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center bg-primary text-white">
            <div class="card-body">
                <i class="fas fa-users fa-3x mb-3"></i>
                <h3>Guests</h3>
                <p>User Management</p>
                <a href="/admin/accounts/user/" class="btn btn-light btn-sm">View All</a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-check fa-2x text-primary mb-2"></i>
                                <h6>Booking Management</h6>
                                <p class="small text-muted">Review and manage all bookings</p>
                                <a href="{% url 'bookings:admin_bookings' %}" class="btn btn-primary btn-sm">Manage Bookings</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-home fa-2x text-success mb-2"></i>
                                <h6>Room Management</h6>
                                <p class="small text-muted">Add, edit, and manage rooms</p>
                                <a href="{% url 'bookings:admin_rooms' %}" class="btn btn-success btn-sm">Manage Rooms</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-comments fa-2x text-warning mb-2"></i>
                                <h6>Messages</h6>
                                <p class="small text-muted">Guest communications</p>
                                <a href="{% url 'messaging:admin_conversations' %}" class="btn btn-warning btn-sm">View Messages</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <i class="fas fa-star fa-2x text-info mb-2"></i>
                                <h6>Reviews</h6>
                                <p class="small text-muted">Moderate guest reviews</p>
                                <a href="{% url 'reviews:admin_reviews' %}" class="btn btn-info btn-sm">Review Management</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tools"></i> System Tools</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/admin/" class="btn btn-dark">
                        <i class="fas fa-cog"></i> Django Admin
                    </a>
                    <a href="{% url 'bookings:room_create' %}" class="btn btn-success">
                        <i class="fas fa-plus"></i> Add New Room
                    </a>
                    <a href="{% url 'messaging:send_notification' %}" class="btn btn-info">
                        <i class="fas fa-bell"></i> Send Notification
                    </a>
                    <a href="{% url 'bookings:availability_calendar' %}" class="btn btn-primary">
                        <i class="fas fa-calendar"></i> Availability Calendar
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-exclamation-triangle"></i> Alerts</h5>
            </div>
            <div class="card-body">
                {% if pending_bookings > 0 %}
                <div class="alert alert-warning">
                    <strong>{{ pending_bookings }}</strong> booking(s) require your attention
                </div>
                {% endif %}
                {% if recent_reviews > 0 %}
                <div class="alert alert-info">
                    <strong>{{ recent_reviews }}</strong> review(s) pending approval
                </div>
                {% endif %}
                {% if pending_bookings == 0 and recent_reviews == 0 %}
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> All caught up!
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.admin-dashboard-container {
    background: linear-gradient(135deg, rgba(240, 255, 240, 0.9), rgba(144, 238, 144, 0.1));
    min-height: 80vh;
    padding: 20px;
    border-radius: 15px;
}

.dashboard-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 30px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.dashboard-actions {
    margin-top: 20px;
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 5px solid var(--accent-green);
    margin-bottom: 20px;
}

.stat-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 15px 35px rgba(50, 205, 50, 0.3);
    border-left-color: var(--dark-green);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--accent-green);
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-label {
    font-size: 1.1rem;
    color: var(--text-dark);
    font-weight: 500;
}

.stat-icon {
    font-size: 3rem;
    color: var(--primary-green);
    margin-bottom: 15px;
    opacity: 0.8;
}

.recent-activity {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.activity-item {
    padding: 15px;
    border-left: 4px solid var(--primary-green);
    margin-bottom: 15px;
    background: rgba(240, 255, 240, 0.5);
    border-radius: 0 10px 10px 0;
    transition: all 0.3s ease;
}

.activity-item:hover {
    transform: translateX(5px);
    border-left-color: var(--accent-green);
    background: rgba(50, 205, 50, 0.1);
}

.quick-actions {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.action-btn {
    display: block;
    width: 100%;
    margin-bottom: 10px;
    padding: 15px;
    border-radius: 25px;
    text-decoration: none;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, var(--primary-green), var(--accent-green));
    color: white;
    border: none;
    font-weight: 500;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(50, 205, 50, 0.4);
    color: white;
    text-decoration: none;
}

@media (max-width: 768px) {
    .dashboard-actions {
        flex-direction: column;
        align-items: center;
    }

    .dashboard-actions .btn {
        width: 100%;
        max-width: 300px;
    }
}
</style>
{% endblock %}
