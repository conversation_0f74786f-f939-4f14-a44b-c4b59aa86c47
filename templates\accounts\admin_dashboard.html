{% extends 'base.html' %}

{% block title %}Admin Dashboard - Carthage Hill Guest House{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-cogs"></i> Admin Dashboard</h2>
        <p class="text-muted">Manage bookings, rooms, and guest services</p>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center bg-warning text-dark">
            <div class="card-body">
                <i class="fas fa-clock fa-3x mb-3"></i>
                <h3>{{ pending_bookings }}</h3>
                <p>Pending Bookings</p>
                <a href="{% url 'bookings:admin_bookings' %}?status=pending" class="btn btn-dark btn-sm">Review</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center bg-info text-white">
            <div class="card-body">
                <i class="fas fa-bed fa-3x mb-3"></i>
                <h3>{{ total_rooms }}</h3>
                <p>Total Rooms</p>
                <a href="{% url 'bookings:admin_rooms' %}" class="btn btn-light btn-sm">Manage</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center bg-success text-white">
            <div class="card-body">
                <i class="fas fa-star fa-3x mb-3"></i>
                <h3>{{ recent_reviews }}</h3>
                <p>Pending Reviews</p>
                <a href="{% url 'reviews:admin_reviews' %}" class="btn btn-light btn-sm">Approve</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center bg-primary text-white">
            <div class="card-body">
                <i class="fas fa-users fa-3x mb-3"></i>
                <h3>Guests</h3>
                <p>User Management</p>
                <a href="/admin/accounts/user/" class="btn btn-light btn-sm">View All</a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-check fa-2x text-primary mb-2"></i>
                                <h6>Booking Management</h6>
                                <p class="small text-muted">Review and manage all bookings</p>
                                <a href="{% url 'bookings:admin_bookings' %}" class="btn btn-primary btn-sm">Manage Bookings</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-home fa-2x text-success mb-2"></i>
                                <h6>Room Management</h6>
                                <p class="small text-muted">Add, edit, and manage rooms</p>
                                <a href="{% url 'bookings:admin_rooms' %}" class="btn btn-success btn-sm">Manage Rooms</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-comments fa-2x text-warning mb-2"></i>
                                <h6>Messages</h6>
                                <p class="small text-muted">Guest communications</p>
                                <a href="{% url 'messaging:admin_conversations' %}" class="btn btn-warning btn-sm">View Messages</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <i class="fas fa-star fa-2x text-info mb-2"></i>
                                <h6>Reviews</h6>
                                <p class="small text-muted">Moderate guest reviews</p>
                                <a href="{% url 'reviews:admin_reviews' %}" class="btn btn-info btn-sm">Review Management</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tools"></i> System Tools</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/admin/" class="btn btn-dark">
                        <i class="fas fa-cog"></i> Django Admin
                    </a>
                    <a href="{% url 'bookings:room_create' %}" class="btn btn-success">
                        <i class="fas fa-plus"></i> Add New Room
                    </a>
                    <a href="{% url 'messaging:send_notification' %}" class="btn btn-info">
                        <i class="fas fa-bell"></i> Send Notification
                    </a>
                    <a href="{% url 'bookings:availability_calendar' %}" class="btn btn-primary">
                        <i class="fas fa-calendar"></i> Availability Calendar
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-exclamation-triangle"></i> Alerts</h5>
            </div>
            <div class="card-body">
                {% if pending_bookings > 0 %}
                <div class="alert alert-warning">
                    <strong>{{ pending_bookings }}</strong> booking(s) require your attention
                </div>
                {% endif %}
                {% if recent_reviews > 0 %}
                <div class="alert alert-info">
                    <strong>{{ recent_reviews }}</strong> review(s) pending approval
                </div>
                {% endif %}
                {% if pending_bookings == 0 and recent_reviews == 0 %}
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> All caught up!
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
