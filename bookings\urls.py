from django.urls import path
from . import views

app_name = 'bookings'

urlpatterns = [
    # Room URLs
    path('rooms/', views.RoomListView.as_view(), name='room_list'),
    path('rooms/<int:pk>/', views.RoomDetailView.as_view(), name='room_detail'),
    
    # Booking URLs
    path('book/<int:room_id>/', views.create_booking, name='create_booking'),
    path('booking/<int:pk>/', views.BookingDetailView.as_view(), name='booking_detail'),
    path('my-bookings/', views.MyBookingsView.as_view(), name='my_bookings'),
    path('booking/<int:booking_id>/cancel/', views.cancel_booking, name='cancel_booking'),
    path('booking/<int:booking_id>/modify/', views.modify_booking, name='modify_booking'),
    
    # Admin URLs
    path('admin/bookings/', views.AdminBookingsView.as_view(), name='admin_bookings'),
    path('admin/booking/<int:booking_id>/approve/', views.approve_booking, name='approve_booking'),
    path('admin/booking/<int:booking_id>/reject/', views.reject_booking, name='reject_booking'),
    path('admin/rooms/', views.AdminRoomsView.as_view(), name='admin_rooms'),
    path('admin/room/create/', views.RoomCreateView.as_view(), name='room_create'),
    path('admin/room/<int:pk>/edit/', views.RoomUpdateView.as_view(), name='room_update'),
    path('admin/calendar/', views.availability_calendar, name='availability_calendar'),
]
