from django.contrib import admin
from .models import Room, Booking

@admin.register(Room)
class RoomAdmin(admin.ModelAdmin):
    list_display = ('name', 'room_type', 'capacity', 'price_per_night', 'is_available', 'created_at')
    list_filter = ('room_type', 'is_available', 'capacity')
    search_fields = ('name', 'description')
    list_editable = ('is_available', 'price_per_night')
    ordering = ('name',)

@admin.register(Booking)
class BookingAdmin(admin.ModelAdmin):
    list_display = ('booking_reference', 'user', 'room', 'check_in_date', 'check_out_date', 'status', 'total_price', 'created_at')
    list_filter = ('status', 'check_in_date', 'check_out_date', 'created_at')
    search_fields = ('booking_reference', 'user__username', 'user__email', 'room__name')
    list_editable = ('status',)
    readonly_fields = ('booking_reference', 'total_price', 'created_at', 'updated_at')
    ordering = ('-created_at',)

    fieldsets = (
        ('Booking Information', {
            'fields': ('booking_reference', 'user', 'room', 'status')
        }),
        ('Dates and Guests', {
            'fields': ('check_in_date', 'check_out_date', 'guests_count')
        }),
        ('Pricing', {
            'fields': ('total_price',)
        }),
        ('Additional Information', {
            'fields': ('special_requests', 'confirmed_by', 'confirmed_at')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )
