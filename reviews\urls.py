from django.urls import path
from . import views

app_name = 'reviews'

urlpatterns = [
    # Public review URLs
    path('', views.ReviewListView.as_view(), name='review_list'),
    path('<int:pk>/', views.ReviewDetailView.as_view(), name='review_detail'),
    
    # User review URLs
    path('create/<int:booking_id>/', views.create_review, name='create_review'),
    path('my-reviews/', views.MyReviewsView.as_view(), name='my_reviews'),
    path('edit/<int:review_id>/', views.edit_review, name='edit_review'),
    
    # Admin review URLs
    path('admin/', views.AdminReviewListView.as_view(), name='admin_reviews'),
    path('admin/<int:review_id>/approve/', views.approve_review, name='approve_review'),
    path('admin/<int:review_id>/reject/', views.reject_review, name='reject_review'),
]
