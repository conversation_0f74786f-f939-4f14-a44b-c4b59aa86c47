from django.urls import path
from . import views

app_name = 'messaging'

urlpatterns = [
    # Conversation URLs
    path('conversations/', views.ConversationListView.as_view(), name='conversations'),
    path('conversation/<int:pk>/', views.ConversationDetailView.as_view(), name='conversation_detail'),
    path('start-conversation/', views.start_conversation, name='start_conversation'),
    path('conversation/<int:conversation_id>/send/', views.send_message, name='send_message'),
    
    # Notification URLs
    path('notifications/', views.NotificationListView.as_view(), name='notifications'),
    path('notification/<int:notification_id>/read/', views.mark_notification_read, name='mark_notification_read'),
    path('notifications/mark-all-read/', views.mark_all_notifications_read, name='mark_all_notifications_read'),
    
    # Admin URLs
    path('admin/conversations/', views.AdminConversationListView.as_view(), name='admin_conversations'),
    path('admin/send-notification/', views.send_notification, name='send_notification'),
]
