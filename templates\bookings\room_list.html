{% extends 'base.html' %}
{% load crispy_forms_tags %}

{% block title %}Available Rooms - Carthage Hill Guest House{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-bed"></i> Available Rooms</h2>
        <p class="text-muted">Find the perfect room for your stay</p>
    </div>
</div>

<!-- Search Form -->
<div class="card mb-4">
    <div class="card-header">
        <h5><i class="fas fa-search"></i> Search Rooms</h5>
    </div>
    <div class="card-body">
        <form method="get">
            {{ search_form|crispy }}
        </form>
    </div>
</div>

<!-- Room Results -->
<div class="row">
    {% for room in rooms %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            {% if room.room_image %}
            <img src="{{ room.room_image.url }}" class="card-img-top" alt="{{ room.name }}" style="height: 200px; object-fit: cover;">
            {% else %}
            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                <i class="fas fa-bed fa-3x text-muted"></i>
            </div>
            {% endif %}
            
            <div class="card-body d-flex flex-column">
                <h5 class="card-title">{{ room.name }}</h5>
                <p class="card-text">
                    <span class="badge bg-primary">{{ room.get_room_type_display }}</span>
                    <span class="badge bg-info">{{ room.capacity }} Guest{{ room.capacity|pluralize }}</span>
                </p>
                <p class="card-text">{{ room.description|truncatewords:20 }}</p>
                
                <div class="mt-auto">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="h5 text-success mb-0">${{ room.price_per_night }}/night</span>
                        {% if room.is_available %}
                        <span class="badge bg-success">Available</span>
                        {% else %}
                        <span class="badge bg-danger">Unavailable</span>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid">
                        <a href="{% url 'bookings:room_detail' room.pk %}" class="btn btn-primary">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h4>No rooms found</h4>
                <p class="text-muted">Try adjusting your search criteria</p>
                <a href="{% url 'bookings:room_list' %}" class="btn btn-primary">View All Rooms</a>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if is_paginated %}
<nav aria-label="Room pagination">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
        <li class="page-item">
            <a class="page-link" href="?page=1{% if request.GET.check_in_date %}&check_in_date={{ request.GET.check_in_date }}{% endif %}{% if request.GET.check_out_date %}&check_out_date={{ request.GET.check_out_date }}{% endif %}{% if request.GET.guests_count %}&guests_count={{ request.GET.guests_count }}{% endif %}{% if request.GET.room_type %}&room_type={{ request.GET.room_type }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}">First</a>
        </li>
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.check_in_date %}&check_in_date={{ request.GET.check_in_date }}{% endif %}{% if request.GET.check_out_date %}&check_out_date={{ request.GET.check_out_date }}{% endif %}{% if request.GET.guests_count %}&guests_count={{ request.GET.guests_count }}{% endif %}{% if request.GET.room_type %}&room_type={{ request.GET.room_type }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}">Previous</a>
        </li>
        {% endif %}
        
        <li class="page-item active">
            <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
        </li>
        
        {% if page_obj.has_next %}
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.check_in_date %}&check_in_date={{ request.GET.check_in_date }}{% endif %}{% if request.GET.check_out_date %}&check_out_date={{ request.GET.check_out_date }}{% endif %}{% if request.GET.guests_count %}&guests_count={{ request.GET.guests_count }}{% endif %}{% if request.GET.room_type %}&room_type={{ request.GET.room_type }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}">Next</a>
        </li>
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.check_in_date %}&check_in_date={{ request.GET.check_in_date }}{% endif %}{% if request.GET.check_out_date %}&check_out_date={{ request.GET.check_out_date }}{% endif %}{% if request.GET.guests_count %}&guests_count={{ request.GET.guests_count }}{% endif %}{% if request.GET.room_type %}&room_type={{ request.GET.room_type }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}">Last</a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}
