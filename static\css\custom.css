/* Carthage Hill Guest House Custom Styles */

:root {
    --primary-green: #90EE90;
    --dark-green: #228B22;
    --light-green: #F0FFF0;
    --accent-green: #32CD32;
    --hover-green: #7FDD7F;
    --text-dark: #2C3E50;
    --text-light: #6C757D;
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.15);
    --border-radius: 10px;
    --transition: all 0.3s ease;
}

/* Global Styles */
body {
    background: linear-gradient(135deg, var(--light-green) 0%, #FFFFFF 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-dark);
    line-height: 1.6;
}

/* Navigation Enhancements */
.navbar {
    background: linear-gradient(135deg, var(--primary-green), var(--accent-green));
    box-shadow: 0 4px 8px var(--shadow-light);
    padding: 1rem 0;
    transition: var(--transition);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--dark-green) !important;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.5);
}

.navbar-nav .nav-link {
    color: var(--dark-green) !important;
    font-weight: 500;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 6px 20px var(--shadow-light);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px var(--shadow-medium);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-green), var(--accent-green));
    border: none;
    color: var(--dark-green);
    font-weight: 600;
    padding: 1.25rem;
}

.card-body {
    padding: 1.5rem;
}

/* Button Enhancements */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--dark-green), var(--accent-green));
    box-shadow: 0 4px 15px rgba(34, 139, 34, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--accent-green), var(--dark-green));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(34, 139, 34, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--accent-green), var(--primary-green));
    box-shadow: 0 4px 15px rgba(50, 205, 50, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--primary-green), var(--accent-green));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(50, 205, 50, 0.4);
}

/* Form Enhancements */
.form-control, .form-select {
    border: 2px solid #E9ECEF;
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    background-color: #FFFFFF;
}

.form-control:focus, .form-select:focus {
    border-color: var(--accent-green);
    box-shadow: 0 0 0 0.2rem rgba(50, 205, 50, 0.25);
    background-color: #FFFFFF;
}

.form-label {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px var(--shadow-light);
}

.alert-success {
    background: linear-gradient(135deg, #D4EDDA, #C3E6CB);
    color: #155724;
}

.alert-warning {
    background: linear-gradient(135deg, #FFF3CD, #FFEAA7);
    color: #856404;
}

.alert-danger {
    background: linear-gradient(135deg, #F8D7DA, #F5C6CB);
    color: #721C24;
}

/* Badge Enhancements */
.badge {
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Table Enhancements */
.table {
    background-color: #FFFFFF;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 4px 15px var(--shadow-light);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-green), var(--accent-green));
    color: var(--dark-green);
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(144, 238, 144, 0.1);
}

/* Footer Enhancements */
.footer {
    background: linear-gradient(135deg, var(--dark-green), #1E7E1E);
    color: #FFFFFF;
    padding: 3rem 0 2rem;
    margin-top: 4rem;
}

/* Notification Badge */
.notification-badge {
    background: linear-gradient(135deg, #DC3545, #C82333);
    color: white;
    border-radius: 50%;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    position: absolute;
    top: -8px;
    right: -8px;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Room Card Specific Styles */
.room-card {
    transition: var(--transition);
    height: 100%;
}

.room-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px var(--shadow-medium);
}

.room-image {
    height: 250px;
    object-fit: cover;
    transition: var(--transition);
}

.room-card:hover .room-image {
    transform: scale(1.05);
}

/* Star Rating */
.star-rating {
    color: #FFD700;
    font-size: 1.2rem;
}

.star-rating .empty {
    color: #E9ECEF;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(50, 205, 50, 0.3);
    border-radius: 50%;
    border-top-color: var(--accent-green);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .room-image {
        height: 200px;
    }
}

/* Chat Styles */
.chat-container {
    height: 400px;
    overflow-y: auto;
    border: 1px solid #E9ECEF;
    border-radius: var(--border-radius);
    padding: 1rem;
    background-color: #FFFFFF;
}

.message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    max-width: 70%;
}

.message.sent {
    background: linear-gradient(135deg, var(--accent-green), var(--primary-green));
    color: white;
    margin-left: auto;
    text-align: right;
}

.message.received {
    background-color: #F8F9FA;
    color: var(--text-dark);
}

/* Dashboard Stats */
.stat-card {
    background: linear-gradient(135deg, #FFFFFF, #F8F9FA);
    border-left: 4px solid var(--accent-green);
    transition: var(--transition);
}

.stat-card:hover {
    border-left-color: var(--dark-green);
    transform: translateX(5px);
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

/* Calendar Styles */
.calendar-day {
    min-height: 100px;
    border: 1px solid #E9ECEF;
    padding: 0.5rem;
    transition: var(--transition);
}

.calendar-day:hover {
    background-color: rgba(144, 238, 144, 0.1);
}

.calendar-booking {
    background: linear-gradient(135deg, var(--accent-green), var(--primary-green));
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}
