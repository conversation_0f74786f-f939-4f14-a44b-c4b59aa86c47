/* Carthage Hill Guest House Custom Styles */

:root {
    --primary-green: #90EE90;
    --dark-green: #228B22;
    --light-green: #F0FFF0;
    --accent-green: #32CD32;
    --hover-green: #7FDD7F;
    --text-dark: #2C3E50;
    --text-light: #6C757D;
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.15);
    --border-radius: 10px;
    --transition: all 0.3s ease;
}

/* Global Styles */
body {
    background: linear-gradient(135deg, var(--light-green) 0%, #FFFFFF 100%);
    background-attachment: fixed;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-dark);
    line-height: 1.6;
    position: relative;
}

/* Background Image Overlay */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="%23228B22" opacity="0.1"/><circle cx="80" cy="40" r="1" fill="%2332CD32" opacity="0.1"/><circle cx="40" cy="80" r="1" fill="%2390EE90" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    z-index: -1;
    pointer-events: none;
}

/* Navigation Enhancements */
.navbar {
    background: linear-gradient(135deg, var(--primary-green), var(--accent-green));
    box-shadow: 0 4px 8px var(--shadow-light);
    padding: 1rem 0;
    transition: var(--transition);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--dark-green) !important;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.5);
}

.navbar-nav .nav-link {
    color: var(--dark-green) !important;
    font-weight: 500;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 6px 20px var(--shadow-light);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px var(--shadow-medium);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-green), var(--accent-green));
    border: none;
    color: var(--dark-green);
    font-weight: 600;
    padding: 1.25rem;
}

.card-body {
    padding: 1.5rem;
}

/* Button Enhancements */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: var(--transition);
    border: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--dark-green), var(--accent-green));
    box-shadow: 0 4px 15px rgba(34, 139, 34, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--accent-green), var(--dark-green));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(34, 139, 34, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--accent-green), var(--primary-green));
    box-shadow: 0 4px 15px rgba(50, 205, 50, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--primary-green), var(--accent-green));
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(50, 205, 50, 0.4);
}

/* Form Enhancements */
.form-control, .form-select {
    border: 2px solid #E9ECEF;
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    background-color: #FFFFFF;
}

.form-control:focus, .form-select:focus {
    border-color: var(--accent-green);
    box-shadow: 0 0 0 0.2rem rgba(50, 205, 50, 0.25);
    background-color: #FFFFFF;
}

.form-label {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px var(--shadow-light);
}

.alert-success {
    background: linear-gradient(135deg, #D4EDDA, #C3E6CB);
    color: #155724;
}

.alert-warning {
    background: linear-gradient(135deg, #FFF3CD, #FFEAA7);
    color: #856404;
}

.alert-danger {
    background: linear-gradient(135deg, #F8D7DA, #F5C6CB);
    color: #721C24;
}

/* Badge Enhancements */
.badge {
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Table Enhancements */
.table {
    background-color: #FFFFFF;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 4px 15px var(--shadow-light);
}

.table thead th {
    background: linear-gradient(135deg, var(--primary-green), var(--accent-green));
    color: var(--dark-green);
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(144, 238, 144, 0.1);
}

/* Footer Enhancements */
.footer {
    background: linear-gradient(135deg, var(--dark-green), #1E7E1E);
    color: #FFFFFF;
    padding: 3rem 0 2rem;
    margin-top: 4rem;
}

/* Notification Badge */
.notification-badge {
    background: linear-gradient(135deg, #DC3545, #C82333);
    color: white;
    border-radius: 50%;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    position: absolute;
    top: -8px;
    right: -8px;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Room Card Specific Styles */
.room-card {
    transition: var(--transition);
    height: 100%;
}

.room-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px var(--shadow-medium);
}

.room-image {
    height: 250px;
    object-fit: cover;
    transition: var(--transition);
}

.room-card:hover .room-image {
    transform: scale(1.05);
}

/* Star Rating */
.star-rating {
    color: #FFD700;
    font-size: 1.2rem;
}

.star-rating .empty {
    color: #E9ECEF;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(50, 205, 50, 0.3);
    border-radius: 50%;
    border-top-color: var(--accent-green);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .room-image {
        height: 200px;
    }
}

/* Chat Styles */
.chat-container {
    height: 400px;
    overflow-y: auto;
    border: 1px solid #E9ECEF;
    border-radius: var(--border-radius);
    padding: 1rem;
    background-color: #FFFFFF;
}

.message {
    margin-bottom: 1rem;
    padding: 0.75rem;
    border-radius: var(--border-radius);
    max-width: 70%;
}

.message.sent {
    background: linear-gradient(135deg, var(--accent-green), var(--primary-green));
    color: white;
    margin-left: auto;
    text-align: right;
}

.message.received {
    background-color: #F8F9FA;
    color: var(--text-dark);
}

/* Dashboard Stats */
.stat-card {
    background: linear-gradient(135deg, #FFFFFF, #F8F9FA);
    border-left: 4px solid var(--accent-green);
    transition: var(--transition);
}

.stat-card:hover {
    border-left-color: var(--dark-green);
    transform: translateX(5px);
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

/* Calendar Styles */
.calendar-day {
    min-height: 100px;
    border: 1px solid #E9ECEF;
    padding: 0.5rem;
    transition: var(--transition);
}

.calendar-day:hover {
    background-color: rgba(144, 238, 144, 0.1);
}

.calendar-booking {
    background: linear-gradient(135deg, var(--accent-green), var(--primary-green));
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
}

/* Enhanced Animations and Effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Enhanced Card Hover Effects */
.card {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.card:hover::before {
    left: 100%;
}

.card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(50, 205, 50, 0.3);
}

/* Enhanced Button Effects */
.btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border-radius: 25px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(50, 205, 50, 0.4);
}

/* Loading Animation */
.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Fade In Animation Class */
.fade-in {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in {
    animation: slideInLeft 0.6s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

.bounce-animation {
    animation: bounce 1s infinite;
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2rem;
    }

    .hero-section p {
        font-size: 1rem;
    }

    .stats-card {
        margin-bottom: 1rem;
    }

    .room-card {
        margin-bottom: 1.5rem;
    }

    .card:hover {
        transform: translateY(-5px) scale(1.01);
    }
}
