{% load crispy_forms_field %}

<div{% if div.css_id %} id="{{ div.css_id }}"{% endif %} class="form-group{% if 'form-horizontal' in form_class %} row{% endif %}{% if wrapper_class %} {{ wrapper_class }}{% endif %}{% if field.css_classes %} {{ field.css_classes }}{% endif %}{% if div.css_class %} {{ div.css_class }}{% endif %}" {{ div.flat_attrs }}>
    {% if field.label and form_show_labels %}
        <label for="{{ field.id_for_label }}" class="{% if 'form-horizontal' in form_class %}col-form-label {% endif %}{{ label_class }}{% if field.field.required %} requiredField{% endif %}">
            {{ field.label }}{% if field.field.required %}<span class="asteriskField">*</span>{% endif %}
        </label>
    {% endif %}

    <div class="{{ field_class }}">
        <div class="input-group {% if div.input_size %} {{ div.input_size }}{% endif %}">
            {% crispy_field field 'class' 'form-control' %}
            <span class="input-group-append{% if active %} active{% endif %}">{{ buttons }}</span>
        </div>
        {% include 'bootstrap4/layout/help_text_and_errors.html' %}
    </div>
</div>
