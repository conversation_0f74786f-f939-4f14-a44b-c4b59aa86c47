import React, { useState, useEffect } from 'react';
import { View, FlatList, RefreshControl, TouchableOpacity } from 'react-native';
import { Text, Card, Title, Chip, ActivityIndicator } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { apiService } from '../services/apiService';
import { theme, styles } from '../theme/theme';

export default function BookingsScreen({ navigation }) {
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadBookings();
  }, []);

  const loadBookings = async () => {
    try {
      const response = await apiService.getBookings();
      setBookings(response.data.results || response.data || []);
    } catch (error) {
      console.error('Error loading bookings:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadBookings();
    setRefreshing(false);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed': return theme.colors.success;
      case 'pending': return theme.colors.warning;
      case 'cancelled': return theme.colors.error;
      default: return theme.colors.placeholder;
    }
  };

  const renderBooking = ({ item }) => (
    <TouchableOpacity
      onPress={() => navigation.navigate('BookingDetail', { bookingId: item.id })}>
      <Card style={[styles.card, { marginHorizontal: 15, marginVertical: 8 }]}>
        <Card.Content>
          <View style={styles.row}>
            <View style={{ flex: 1 }}>
              <Title>{item.room_details?.name}</Title>
              <Text style={styles.text}>
                {new Date(item.check_in_date).toLocaleDateString()} - {new Date(item.check_out_date).toLocaleDateString()}
              </Text>
              <Text style={styles.smallText}>Ref: {item.booking_reference}</Text>
            </View>
            <Chip style={{ backgroundColor: getStatusColor(item.status) }} textStyle={{ color: '#fff' }}>
              {item.status}
            </Chip>
          </View>
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={[styles.container, styles.center]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={bookings}
        renderItem={renderBooking}
        keyExtractor={(item) => item.id.toString()}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        ListEmptyComponent={
          <View style={[styles.center, { padding: 50 }]}>
            <Icon name="event-busy" size={60} color={theme.colors.placeholder} />
            <Text style={styles.text}>No bookings found</Text>
          </View>
        }
      />
    </View>
  );
}
