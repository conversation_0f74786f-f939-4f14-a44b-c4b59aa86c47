from django.shortcuts import render, redirect
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from .forms import CustomUserCreationForm, UserProfileForm
from .models import User

class RegisterView(CreateView):
    model = User
    form_class = CustomUserCreationForm
    template_name = 'accounts/register.html'
    success_url = reverse_lazy('accounts:login')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Registration successful! You can now log in.')
        return response

    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            return redirect('accounts:dashboard')
        return super().dispatch(request, *args, **kwargs)

class ProfileView(LoginRequiredMixin, UpdateView):
    model = User
    form_class = UserProfileForm
    template_name = 'accounts/profile.html'
    success_url = reverse_lazy('accounts:profile')

    def get_object(self):
        return self.request.user

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Profile updated successfully!')
        return response

@login_required
def dashboard(request):
    """Main dashboard view that redirects based on user type"""
    if request.user.is_admin:
        return redirect('accounts:admin_dashboard')
    else:
        return redirect('accounts:client_dashboard')

@login_required
def client_dashboard(request):
    """Client dashboard with booking overview"""
    from bookings.models import Booking
    from messaging.models import Notification

    recent_bookings = Booking.objects.filter(user=request.user).order_by('-created_at')[:5]
    unread_notifications = Notification.objects.filter(user=request.user, is_read=False).count()

    context = {
        'recent_bookings': recent_bookings,
        'unread_notifications': unread_notifications,
    }
    return render(request, 'accounts/client_dashboard.html', context)

@login_required
def admin_dashboard(request):
    """Admin dashboard with system overview"""
    if not request.user.is_admin:
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('accounts:client_dashboard')

    from bookings.models import Booking, Room
    from reviews.models import Review
    from messaging.models import Notification, Conversation
    from django.utils import timezone
    from datetime import timedelta

    # Basic stats
    pending_bookings = Booking.objects.filter(status='pending').count()
    total_bookings = Booking.objects.count()
    total_rooms = Room.objects.count()
    available_rooms = Room.objects.filter(is_available=True).count()
    pending_reviews = Review.objects.filter(is_approved=False).count()

    # Recent activity
    recent_bookings = Booking.objects.filter(
        created_at__gte=timezone.now() - timedelta(days=7)
    ).order_by('-created_at')[:5]

    recent_reviews = Review.objects.filter(
        created_at__gte=timezone.now() - timedelta(days=7)
    ).order_by('-created_at')[:5]

    # Revenue stats
    confirmed_bookings = Booking.objects.filter(status='confirmed')
    total_revenue = sum(booking.total_price for booking in confirmed_bookings)
    monthly_revenue = sum(
        booking.total_price for booking in confirmed_bookings.filter(
            created_at__gte=timezone.now() - timedelta(days=30)
        )
    )

    # User stats
    total_clients = User.objects.filter(user_type='client').count()
    new_clients = User.objects.filter(
        user_type='client',
        date_joined__gte=timezone.now() - timedelta(days=30)
    ).count()

    # Messaging stats
    unread_conversations = Conversation.objects.filter(
        messages__is_read=False,
        messages__sender__user_type='client'
    ).distinct().count()

    context = {
        'pending_bookings': pending_bookings,
        'total_bookings': total_bookings,
        'total_rooms': total_rooms,
        'available_rooms': available_rooms,
        'pending_reviews': pending_reviews,
        'recent_bookings': recent_bookings,
        'recent_reviews': recent_reviews,
        'total_revenue': total_revenue,
        'monthly_revenue': monthly_revenue,
        'total_clients': total_clients,
        'new_clients': new_clients,
        'unread_conversations': unread_conversations,
    }
    return render(request, 'accounts/admin_dashboard.html', context)
