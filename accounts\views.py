from django.shortcuts import render, redirect
from django.contrib.auth import login, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic import CreateView, UpdateView
from django.contrib.auth.mixins import LoginRequiredMixin
from .forms import CustomUserCreationForm, UserProfileForm
from .models import User

class RegisterView(CreateView):
    model = User
    form_class = CustomUserCreationForm
    template_name = 'accounts/register.html'
    success_url = reverse_lazy('accounts:login')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Registration successful! You can now log in.')
        return response

    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            return redirect('dashboard')
        return super().dispatch(request, *args, **kwargs)

class ProfileView(LoginRequiredMixin, UpdateView):
    model = User
    form_class = UserProfileForm
    template_name = 'accounts/profile.html'
    success_url = reverse_lazy('accounts:profile')

    def get_object(self):
        return self.request.user

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Profile updated successfully!')
        return response

@login_required
def dashboard(request):
    """Main dashboard view that redirects based on user type"""
    if request.user.is_admin:
        return redirect('admin_dashboard')
    else:
        return redirect('client_dashboard')

@login_required
def client_dashboard(request):
    """Client dashboard with booking overview"""
    from bookings.models import Booking
    from messaging.models import Notification

    recent_bookings = Booking.objects.filter(user=request.user).order_by('-created_at')[:5]
    unread_notifications = Notification.objects.filter(user=request.user, is_read=False).count()

    context = {
        'recent_bookings': recent_bookings,
        'unread_notifications': unread_notifications,
    }
    return render(request, 'accounts/client_dashboard.html', context)

@login_required
def admin_dashboard(request):
    """Admin dashboard with system overview"""
    if not request.user.is_admin:
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('client_dashboard')

    from bookings.models import Booking, Room
    from reviews.models import Review

    pending_bookings = Booking.objects.filter(status='pending').count()
    total_rooms = Room.objects.count()
    recent_reviews = Review.objects.filter(is_approved=False).count()

    context = {
        'pending_bookings': pending_bookings,
        'total_rooms': total_rooms,
        'recent_reviews': recent_reviews,
    }
    return render(request, 'accounts/admin_dashboard.html', context)
