{% extends 'base.html' %}

{% block title %}My Bookings - Carthage Hill Guest House{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-calendar-check"></i> My Bookings</h2>
        <p class="text-muted">Manage your reservations</p>
    </div>
</div>

{% if bookings %}
<div class="row">
    {% for booking in bookings %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <strong>{{ booking.booking_reference }}</strong>
                <span class="badge bg-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% elif booking.status == 'cancelled' %}danger{% else %}secondary{% endif %}">
                    {{ booking.get_status_display }}
                </span>
            </div>
            <div class="card-body">
                <h5 class="card-title">{{ booking.room.name }}</h5>
                <p class="card-text">
                    <i class="fas fa-calendar"></i> 
                    {{ booking.check_in_date|date:"M d, Y" }} - {{ booking.check_out_date|date:"M d, Y" }}
                </p>
                <p class="card-text">
                    <i class="fas fa-users"></i> {{ booking.guests_count }} guest{{ booking.guests_count|pluralize }}
                </p>
                <p class="card-text">
                    <i class="fas fa-dollar-sign"></i> ${{ booking.total_price }}
                </p>
                {% if booking.special_requests %}
                <p class="card-text">
                    <small class="text-muted">
                        <i class="fas fa-comment"></i> {{ booking.special_requests|truncatewords:10 }}
                    </small>
                </p>
                {% endif %}
            </div>
            <div class="card-footer">
                <div class="d-grid gap-2">
                    <a href="{% url 'bookings:booking_detail' booking.id %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                    {% if booking.can_be_modified %}
                    <a href="{% url 'bookings:modify_booking' booking.id %}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-edit"></i> Modify
                    </a>
                    {% endif %}
                    {% if booking.can_be_cancelled %}
                    <a href="{% url 'bookings:cancel_booking' booking.id %}" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if is_paginated %}
<nav aria-label="Bookings pagination">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
        <li class="page-item">
            <a class="page-link" href="?page=1">First</a>
        </li>
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
        </li>
        {% endif %}
        
        <li class="page-item active">
            <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
        </li>
        
        {% if page_obj.has_next %}
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
        </li>
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last</a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

{% else %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-calendar-times fa-5x text-muted mb-4"></i>
                <h4>No bookings yet</h4>
                <p class="text-muted">You haven't made any reservations yet. Start by browsing our available rooms.</p>
                <a href="{% url 'bookings:room_list' %}" class="btn btn-primary btn-lg">
                    <i class="fas fa-search"></i> Browse Rooms
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
