from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator

User = get_user_model()

class Review(models.Model):
    booking = models.OneToOneField('bookings.Booking', on_delete=models.CASCADE, related_name='review')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='reviews')
    room = models.ForeignKey('bookings.Room', on_delete=models.CASCADE, related_name='reviews')

    # Rating fields (1-5 stars)
    overall_rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    cleanliness_rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    service_rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    location_rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )
    value_rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)]
    )

    # Review content
    title = models.CharField(max_length=200)
    comment = models.TextField()

    # Additional fields
    would_recommend = models.BooleanField(default=True)
    is_approved = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Review by {self.user.username} for {self.room.name}"

    def get_average_rating(self):
        """Calculate average rating across all categories"""
        ratings = [
            self.overall_rating,
            self.cleanliness_rating,
            self.service_rating,
            self.location_rating,
            self.value_rating
        ]
        return sum(ratings) / len(ratings)

    def save(self, *args, **kwargs):
        # Ensure user is the same as booking user
        if self.booking:
            self.user = self.booking.user
            self.room = self.booking.room
        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-created_at']
        unique_together = ['booking', 'user']
