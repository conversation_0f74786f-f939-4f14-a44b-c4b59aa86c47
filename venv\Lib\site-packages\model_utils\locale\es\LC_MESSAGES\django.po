# This file is distributed under the same license as the django-model-utils package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: django-model-utils\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-04-01 15:01+0200\n"
"PO-Revision-Date: 2020-03-29 11:14+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Lokalize 2.0\n"

#: models.py:24
msgid "created"
msgstr "creado"

#: models.py:25
msgid "modified"
msgstr "modificado"

#: models.py:49
msgid "start"
msgstr "inicio"

#: models.py:50
msgid "end"
msgstr "fin"

#: models.py:65
msgid "status"
msgstr "estado"

#: models.py:66
msgid "status changed"
msgstr "estado modificado"
