from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.authtoken.models import Token
from rest_framework.authtoken.views import ObtainAuthToken
from django.contrib.auth import authenticate, get_user_model
from django.db.models import Q
from bookings.models import Room, Booking
from messaging.models import Conversation, Message, Notification
from reviews.models import Review
from .serializers import (
    UserSerializer, UserRegistrationSerializer, RoomSerializer,
    BookingSerializer, BookingCreateSerializer, MessageSerializer,
    ConversationSerializer, NotificationSerializer, ReviewSerializer,
    ReviewCreateSerializer
)

User = get_user_model()

# Authentication Views
class CustomAuthToken(ObtainAuthToken):
    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data,
                                           context={'request': request})
        serializer.is_valid(raise_exception=True)
        user = serializer.validated_data['user']
        token, created = Token.objects.get_or_create(user=user)
        return Response({
            'token': token.key,
            'user': UserSerializer(user).data
        })

class RegisterView(generics.CreateAPIView):
    queryset = User.objects.all()
    serializer_class = UserRegistrationSerializer
    permission_classes = [permissions.AllowAny]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user = serializer.save()
        token, created = Token.objects.get_or_create(user=user)
        return Response({
            'token': token.key,
            'user': UserSerializer(user).data
        }, status=status.HTTP_201_CREATED)

class ProfileView(generics.RetrieveUpdateAPIView):
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        return self.request.user

# Room Views
class RoomListView(generics.ListAPIView):
    serializer_class = RoomSerializer
    permission_classes = [permissions.AllowAny]

    def get_queryset(self):
        queryset = Room.objects.filter(is_available=True)

        # Filter parameters
        room_type = self.request.query_params.get('room_type')
        min_price = self.request.query_params.get('min_price')
        max_price = self.request.query_params.get('max_price')
        capacity = self.request.query_params.get('capacity')
        check_in = self.request.query_params.get('check_in_date')
        check_out = self.request.query_params.get('check_out_date')

        if room_type:
            queryset = queryset.filter(room_type=room_type)

        if min_price:
            queryset = queryset.filter(price_per_night__gte=min_price)

        if max_price:
            queryset = queryset.filter(price_per_night__lte=max_price)

        if capacity:
            queryset = queryset.filter(capacity__gte=capacity)

        if check_in and check_out:
            # Filter available rooms for date range
            available_room_ids = []
            for room in queryset:
                if room.is_available_for_dates(check_in, check_out):
                    available_room_ids.append(room.id)
            queryset = queryset.filter(id__in=available_room_ids)

        return queryset.order_by('price_per_night')

class RoomDetailView(generics.RetrieveAPIView):
    queryset = Room.objects.filter(is_available=True)
    serializer_class = RoomSerializer
    permission_classes = [permissions.AllowAny]

# Booking Views
class BookingListCreateView(generics.ListCreateAPIView):
    serializer_class = BookingSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Booking.objects.filter(user=self.request.user).order_by('-created_at')

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return BookingCreateSerializer
        return BookingSerializer

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class BookingDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = BookingSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Booking.objects.filter(user=self.request.user)

    def update(self, request, *args, **kwargs):
        booking = self.get_object()
        if not booking.can_be_modified():
            return Response(
                {'error': 'This booking cannot be modified'},
                status=status.HTTP_400_BAD_REQUEST
            )
        return super().update(request, *args, **kwargs)

    def destroy(self, request, *args, **kwargs):
        booking = self.get_object()
        if not booking.can_be_cancelled():
            return Response(
                {'error': 'This booking cannot be cancelled'},
                status=status.HTTP_400_BAD_REQUEST
            )
        booking.status = 'cancelled'
        booking.save()
        return Response(status=status.HTTP_204_NO_CONTENT)

# Messaging Views
class ConversationListView(generics.ListAPIView):
    serializer_class = ConversationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Conversation.objects.filter(
            participants=self.request.user
        ).order_by('-updated_at')

class ConversationDetailView(generics.RetrieveAPIView):
    serializer_class = ConversationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Conversation.objects.filter(participants=self.request.user)

class MessageListCreateView(generics.ListCreateAPIView):
    serializer_class = MessageSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        conversation_id = self.kwargs.get('conversation_id')
        conversation = Conversation.objects.filter(
            id=conversation_id,
            participants=self.request.user
        ).first()

        if conversation:
            # Mark messages as read
            conversation.messages.filter(is_read=False).exclude(
                sender=self.request.user
            ).update(is_read=True)

            return conversation.messages.order_by('created_at')
        return Message.objects.none()

    def perform_create(self, serializer):
        conversation_id = self.kwargs.get('conversation_id')
        conversation = Conversation.objects.filter(
            id=conversation_id,
            participants=self.request.user
        ).first()

        if conversation:
            serializer.save(
                sender=self.request.user,
                conversation=conversation
            )

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def start_conversation(request):
    """Start a new conversation with admin"""
    if request.user.is_admin:
        return Response(
            {'error': 'Admins cannot start conversations with themselves'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Find admin user
    admin_user = User.objects.filter(is_superuser=True).first()
    if not admin_user:
        admin_user = User.objects.filter(user_type='admin').first()

    if not admin_user:
        return Response(
            {'error': 'No admin available for messaging'},
            status=status.HTTP_400_BAD_REQUEST
        )

    # Find or create conversation
    conversation = Conversation.objects.filter(
        participants=request.user
    ).filter(participants=admin_user).first()

    if not conversation:
        conversation = Conversation.objects.create(
            subject=f'Support Request from {request.user.username}'
        )
        conversation.participants.add(request.user, admin_user)

    serializer = ConversationSerializer(conversation, context={'request': request})
    return Response(serializer.data)

# Notification Views
class NotificationListView(generics.ListAPIView):
    serializer_class = NotificationSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Notification.objects.filter(user=self.request.user).order_by('-created_at')

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def mark_notification_read(request, notification_id):
    try:
        notification = Notification.objects.get(
            id=notification_id,
            user=request.user
        )
        notification.is_read = True
        notification.save()
        return Response({'success': True})
    except Notification.DoesNotExist:
        return Response(
            {'error': 'Notification not found'},
            status=status.HTTP_404_NOT_FOUND
        )

@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def mark_all_notifications_read(request):
    Notification.objects.filter(
        user=request.user,
        is_read=False
    ).update(is_read=True)
    return Response({'success': True})

# Review Views
class ReviewListView(generics.ListAPIView):
    serializer_class = ReviewSerializer
    permission_classes = [permissions.AllowAny]

    def get_queryset(self):
        queryset = Review.objects.filter(is_approved=True).order_by('-created_at')

        room_id = self.request.query_params.get('room_id')
        rating = self.request.query_params.get('min_rating')

        if room_id:
            queryset = queryset.filter(room_id=room_id)

        if rating:
            queryset = queryset.filter(overall_rating__gte=rating)

        return queryset

class MyReviewListView(generics.ListAPIView):
    serializer_class = ReviewSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Review.objects.filter(user=self.request.user).order_by('-created_at')

class ReviewCreateView(generics.CreateAPIView):
    serializer_class = ReviewCreateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

class ReviewDetailView(generics.RetrieveUpdateAPIView):
    serializer_class = ReviewSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Review.objects.filter(user=self.request.user)

# Utility Views
@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def room_availability(request, room_id):
    """Check room availability for specific dates"""
    try:
        room = Room.objects.get(id=room_id, is_available=True)
        check_in = request.query_params.get('check_in_date')
        check_out = request.query_params.get('check_out_date')

        if not check_in or not check_out:
            return Response(
                {'error': 'check_in_date and check_out_date are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        is_available = room.is_available_for_dates(check_in, check_out)
        return Response({
            'room_id': room_id,
            'check_in_date': check_in,
            'check_out_date': check_out,
            'is_available': is_available
        })
    except Room.DoesNotExist:
        return Response(
            {'error': 'Room not found'},
            status=status.HTTP_404_NOT_FOUND
        )
