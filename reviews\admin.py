from django.contrib import admin
from .models import Review

@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'room', 'overall_rating', 'get_average_rating', 'is_approved', 'created_at')
    list_filter = ('overall_rating', 'is_approved', 'would_recommend', 'created_at')
    search_fields = ('user__username', 'room__name', 'title', 'comment')
    list_editable = ('is_approved',)
    readonly_fields = ('user', 'room', 'booking', 'created_at', 'updated_at')
    ordering = ('-created_at',)

    fieldsets = (
        ('Review Information', {
            'fields': ('user', 'room', 'booking', 'title', 'comment')
        }),
        ('Ratings', {
            'fields': ('overall_rating', 'cleanliness_rating', 'service_rating', 'location_rating', 'value_rating')
        }),
        ('Additional', {
            'fields': ('would_recommend', 'is_approved')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def get_average_rating(self, obj):
        return round(obj.get_average_rating(), 1)
    get_average_rating.short_description = 'Average Rating'
