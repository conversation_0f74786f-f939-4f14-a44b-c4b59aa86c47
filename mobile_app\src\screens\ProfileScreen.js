import React from 'react';
import { View, ScrollView } from 'react-native';
import { Text, Card, Button } from 'react-native-paper';
import { useAuth } from '../context/AuthContext';
import { styles } from '../theme/theme';

export default function ProfileScreen() {
  const { user, logout } = useAuth();

  return (
    <ScrollView style={styles.container}>
      <Card style={styles.card}>
        <Card.Content>
          <Text style={styles.title}>Profile</Text>
          <Text style={styles.text}>Username: {user?.username}</Text>
          <Text style={styles.text}>Email: {user?.email}</Text>
          <Text style={styles.text}>Name: {user?.first_name} {user?.last_name}</Text>
          <Button mode="contained" onPress={logout} style={styles.button}>
            Logout
          </Button>
        </Card.Content>
      </Card>
    </ScrollView>
  );
}
