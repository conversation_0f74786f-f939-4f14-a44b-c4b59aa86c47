# Deployment Guide - Carthage Hill Guest House

This guide covers deploying both the Django web application and React Native mobile app to production.

## 🌐 Web Application Deployment

### Option 1: Heroku Deployment

1. **Prepare for Heroku**
```bash
pip install gunicorn whitenoise
pip freeze > requirements.txt
```

2. **Create Procfile**
```
web: gunicorn carthage_hill_booking.wsgi
worker: python manage.py runworker
```

3. **Update settings for production**
```python
# settings.py
import os
import dj_database_url

DEBUG = False
ALLOWED_HOSTS = ['your-app-name.herokuapp.com', 'localhost']

# Database
DATABASES = {
    'default': dj_database_url.parse(os.environ.get('DATABASE_URL'))
}

# Static files
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Redis for Channels
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            "hosts": [os.environ.get('REDIS_URL', 'redis://localhost:6379')],
        },
    },
}
```

4. **Deploy to Heroku**
```bash
heroku create your-app-name
heroku addons:create heroku-postgresql:hobby-dev
heroku addons:create heroku-redis:hobby-dev
git push heroku main
heroku run python manage.py migrate
heroku run python manage.py createsuperuser
```

### Option 2: DigitalOcean/VPS Deployment

1. **Server Setup**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install dependencies
sudo apt install python3-pip python3-venv nginx postgresql postgresql-contrib redis-server

# Create user
sudo adduser carthage
sudo usermod -aG sudo carthage
```

2. **Application Setup**
```bash
# Clone repository
git clone <your-repo> /home/<USER>/app
cd /home/<USER>/app

# Create virtual environment
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Configure database
sudo -u postgres createdb carthage_db
sudo -u postgres createuser carthage_user
```

3. **Nginx Configuration**
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location /static/ {
        alias /home/<USER>/app/staticfiles/;
    }

    location /media/ {
        alias /home/<USER>/app/media/;
    }

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /ws/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

4. **Systemd Service**
```ini
[Unit]
Description=Carthage Hill Django App
After=network.target

[Service]
User=carthage
Group=www-data
WorkingDirectory=/home/<USER>/app
Environment="PATH=/home/<USER>/app/venv/bin"
ExecStart=/home/<USER>/app/venv/bin/gunicorn --workers 3 --bind 127.0.0.1:8000 carthage_hill_booking.wsgi:application
Restart=always

[Install]
WantedBy=multi-user.target
```

## 📱 Mobile App Deployment

### Android Deployment

1. **Generate Signing Key**
```bash
keytool -genkeypair -v -storetype PKCS12 -keystore carthage-release-key.keystore -alias carthage-key-alias -keyalg RSA -keysize 2048 -validity 10000
```

2. **Configure Gradle**
```gradle
// android/app/build.gradle
android {
    signingConfigs {
        release {
            storeFile file('carthage-release-key.keystore')
            storePassword 'your-store-password'
            keyAlias 'carthage-key-alias'
            keyPassword 'your-key-password'
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
        }
    }
}
```

3. **Build Release APK**
```bash
cd android
./gradlew assembleRelease
```

4. **Upload to Google Play Store**
- Create developer account
- Upload APK to Play Console
- Fill out store listing
- Submit for review

### iOS Deployment

1. **Configure Xcode Project**
```bash
cd ios
pod install
open CarthageHillGuestHouse.xcworkspace
```

2. **Archive and Upload**
- Select "Any iOS Device" as target
- Product → Archive
- Upload to App Store Connect
- Submit for review

## 🔧 Production Configuration

### Environment Variables
```bash
# .env
DEBUG=False
SECRET_KEY=your-super-secret-key
DATABASE_URL=postgresql://user:pass@localhost/dbname
REDIS_URL=redis://localhost:6379
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
```

### Security Checklist
- [ ] Set DEBUG=False
- [ ] Use strong SECRET_KEY
- [ ] Configure ALLOWED_HOSTS
- [ ] Set up SSL certificate
- [ ] Configure CORS properly
- [ ] Set up database backups
- [ ] Configure monitoring
- [ ] Set up logging
- [ ] Enable security headers
- [ ] Configure firewall

### Performance Optimization
- [ ] Enable database connection pooling
- [ ] Set up Redis caching
- [ ] Configure CDN for static files
- [ ] Enable gzip compression
- [ ] Optimize database queries
- [ ] Set up monitoring (Sentry, New Relic)
- [ ] Configure load balancing

## 📊 Monitoring & Maintenance

### Health Checks
```python
# health_check.py
import requests
import sys

def check_health():
    try:
        response = requests.get('https://your-domain.com/api/health/')
        if response.status_code == 200:
            print("✅ Application is healthy")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

if __name__ == "__main__":
    if not check_health():
        sys.exit(1)
```

### Backup Strategy
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump carthage_db > backup_$DATE.sql
aws s3 cp backup_$DATE.sql s3://your-backup-bucket/
```

### Log Monitoring
```python
# settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/carthage/django.log',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

## 🚀 CI/CD Pipeline

### GitHub Actions Example
```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
    
    - name: Run tests
      run: |
        python manage.py test
    
    - name: Deploy to Heroku
      uses: akhileshns/heroku-deploy@v3.12.12
      with:
        heroku_api_key: ${{secrets.HEROKU_API_KEY}}
        heroku_app_name: "your-app-name"
        heroku_email: "<EMAIL>"
```

## 📞 Support & Troubleshooting

### Common Issues

1. **Static files not loading**
```bash
python manage.py collectstatic --noinput
```

2. **Database connection errors**
```bash
# Check database URL format
DATABASE_URL=postgresql://username:password@host:port/database
```

3. **WebSocket connection issues**
```bash
# Ensure Redis is running
redis-cli ping
```

4. **Mobile app API connection**
```javascript
// Update API base URL for production
const BASE_URL = 'https://your-domain.com/api';
```

### Getting Help
- Check application logs
- Monitor error tracking (Sentry)
- Review server metrics
- Contact support team

---

This deployment guide ensures your Carthage Hill Guest House booking system runs smoothly in production with proper security, performance, and monitoring.
