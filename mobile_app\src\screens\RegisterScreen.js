import React, { useState } from 'react';
import {
  View,
  ScrollView,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {
  TextInput,
  Button,
  Text,
  Card,
  Title,
  ActivityIndicator,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAuth } from '../context/AuthContext';
import { theme, styles } from '../theme/theme';

export default function RegisterScreen({ navigation }) {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    phone_number: '',
    password: '',
    password_confirm: '',
  });
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const { register } = useAuth();

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = () => {
    const { username, email, first_name, last_name, password, password_confirm } = formData;
    
    if (!username.trim() || !email.trim() || !first_name.trim() || !last_name.trim() || !password) {
      Alert.alert('Error', 'Please fill in all required fields');
      return false;
    }

    if (password !== password_confirm) {
      Alert.alert('Error', 'Passwords do not match');
      return false;
    }

    if (password.length < 8) {
      Alert.alert('Error', 'Password must be at least 8 characters long');
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return false;
    }

    return true;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    setLoading(true);
    const result = await register(formData);
    setLoading(false);

    if (!result.success) {
      const errorMessage = typeof result.error === 'object' 
        ? Object.values(result.error).flat().join('\n')
        : result.error;
      Alert.alert('Registration Failed', errorMessage);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <ScrollView contentContainerStyle={{ padding: 20 }}>
        <Card style={styles.card}>
          <Card.Content>
            <View style={styles.center}>
              <Icon
                name="hotel"
                size={60}
                color={theme.colors.primary}
                style={{ marginBottom: 20 }}
              />
              <Title style={styles.title}>Create Account</Title>
            </View>

            <TextInput
              label="Username *"
              value={formData.username}
              onChangeText={(value) => handleInputChange('username', value)}
              mode="outlined"
              style={styles.input}
              left={<TextInput.Icon icon="account" />}
              autoCapitalize="none"
              autoCorrect={false}
            />

            <TextInput
              label="Email *"
              value={formData.email}
              onChangeText={(value) => handleInputChange('email', value)}
              mode="outlined"
              style={styles.input}
              left={<TextInput.Icon icon="email" />}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />

            <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <TextInput
                label="First Name *"
                value={formData.first_name}
                onChangeText={(value) => handleInputChange('first_name', value)}
                mode="outlined"
                style={[styles.input, { flex: 1, marginRight: 5 }]}
                left={<TextInput.Icon icon="account-circle" />}
              />

              <TextInput
                label="Last Name *"
                value={formData.last_name}
                onChangeText={(value) => handleInputChange('last_name', value)}
                mode="outlined"
                style={[styles.input, { flex: 1, marginLeft: 5 }]}
                left={<TextInput.Icon icon="account-circle" />}
              />
            </View>

            <TextInput
              label="Phone Number"
              value={formData.phone_number}
              onChangeText={(value) => handleInputChange('phone_number', value)}
              mode="outlined"
              style={styles.input}
              left={<TextInput.Icon icon="phone" />}
              keyboardType="phone-pad"
            />

            <TextInput
              label="Password *"
              value={formData.password}
              onChangeText={(value) => handleInputChange('password', value)}
              mode="outlined"
              style={styles.input}
              secureTextEntry={!showPassword}
              left={<TextInput.Icon icon="lock" />}
              right={
                <TextInput.Icon
                  icon={showPassword ? 'eye-off' : 'eye'}
                  onPress={() => setShowPassword(!showPassword)}
                />
              }
            />

            <TextInput
              label="Confirm Password *"
              value={formData.password_confirm}
              onChangeText={(value) => handleInputChange('password_confirm', value)}
              mode="outlined"
              style={styles.input}
              secureTextEntry={!showPassword}
              left={<TextInput.Icon icon="lock-check" />}
            />

            <Button
              mode="contained"
              onPress={handleRegister}
              style={[styles.button, { marginTop: 20 }]}
              disabled={loading}
              contentStyle={{ paddingVertical: 8 }}>
              {loading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                'Create Account'
              )}
            </Button>

            <View style={[styles.row, { marginTop: 20 }]}>
              <Text style={styles.text}>Already have an account? </Text>
              <Button
                mode="text"
                onPress={() => navigation.navigate('Login')}
                compact>
                Sign In
              </Button>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
