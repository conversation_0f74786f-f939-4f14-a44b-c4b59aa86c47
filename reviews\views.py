from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib import messages
from django.views.generic import ListView, DetailView, CreateView, UpdateView
from django.urls import reverse_lazy
from django.db.models import Q, Avg
from bookings.models import Booking
from .models import Review
from .forms import ReviewForm, ReviewFilterForm

class ReviewListView(ListView):
    model = Review
    template_name = 'reviews/review_list.html'
    context_object_name = 'reviews'
    paginate_by = 10

    def get_queryset(self):
        queryset = Review.objects.filter(is_approved=True).order_by('-created_at')
        form = ReviewFilterForm(self.request.GET)

        if form.is_valid():
            rating = form.cleaned_data.get('rating')
            room_type = form.cleaned_data.get('room_type')

            if rating:
                queryset = queryset.filter(overall_rating__gte=int(rating))

            if room_type:
                queryset = queryset.filter(room__room_type=room_type)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['filter_form'] = ReviewFilterForm(self.request.GET)
        context['average_rating'] = Review.objects.filter(is_approved=True).aggregate(
            avg_rating=Avg('overall_rating')
        )['avg_rating'] or 0
        return context

class ReviewDetailView(DetailView):
    model = Review
    template_name = 'reviews/review_detail.html'
    context_object_name = 'review'

    def get_queryset(self):
        return Review.objects.filter(is_approved=True)

@login_required
def create_review(request, booking_id):
    booking = get_object_or_404(Booking, id=booking_id, user=request.user, status='completed')

    # Check if review already exists
    if hasattr(booking, 'review'):
        messages.info(request, 'You have already reviewed this booking.')
        return redirect('reviews:review_detail', booking.review.id)

    if request.method == 'POST':
        form = ReviewForm(request.POST)
        if form.is_valid():
            review = form.save(commit=False)
            review.booking = booking
            review.user = request.user
            review.room = booking.room
            review.save()

            messages.success(request, 'Thank you for your review! It will be published after approval.')
            return redirect('bookings:booking_detail', booking.id)
    else:
        form = ReviewForm()

    return render(request, 'reviews/create_review.html', {
        'form': form,
        'booking': booking
    })

class MyReviewsView(LoginRequiredMixin, ListView):
    model = Review
    template_name = 'reviews/my_reviews.html'
    context_object_name = 'reviews'
    paginate_by = 10

    def get_queryset(self):
        return Review.objects.filter(user=self.request.user).order_by('-created_at')

@login_required
def edit_review(request, review_id):
    review = get_object_or_404(Review, id=review_id, user=request.user)

    if request.method == 'POST':
        form = ReviewForm(request.POST, instance=review)
        if form.is_valid():
            review = form.save(commit=False)
            review.is_approved = False  # Reset approval status
            review.save()

            messages.success(request, 'Review updated successfully! It will be reviewed again.')
            return redirect('reviews:my_reviews')
    else:
        form = ReviewForm(instance=review)

    return render(request, 'reviews/edit_review.html', {
        'form': form,
        'review': review
    })

# Admin Views
class AdminReviewListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    model = Review
    template_name = 'reviews/admin_reviews.html'
    context_object_name = 'reviews'
    paginate_by = 20

    def test_func(self):
        return self.request.user.is_admin

    def get_queryset(self):
        status = self.request.GET.get('status', 'pending')
        if status == 'pending':
            return Review.objects.filter(is_approved=False).order_by('-created_at')
        elif status == 'approved':
            return Review.objects.filter(is_approved=True).order_by('-created_at')
        else:
            return Review.objects.all().order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_filter'] = self.request.GET.get('status', 'pending')
        return context

@login_required
def approve_review(request, review_id):
    if not request.user.is_admin:
        messages.error(request, 'Access denied.')
        return redirect('accounts:dashboard')

    review = get_object_or_404(Review, id=review_id)
    review.is_approved = True
    review.save()

    messages.success(request, f'Review by {review.user.username} approved successfully.')
    return redirect('reviews:admin_reviews')

@login_required
def reject_review(request, review_id):
    if not request.user.is_admin:
        messages.error(request, 'Access denied.')
        return redirect('accounts:dashboard')

    review = get_object_or_404(Review, id=review_id)

    if request.method == 'POST':
        review.delete()
        messages.success(request, 'Review rejected and deleted.')
        return redirect('reviews:admin_reviews')

    return render(request, 'reviews/reject_review.html', {'review': review})
