{% extends 'base.html' %}

{% block title %}Messages - Carthage Hill Guest House{% endblock %}

{% block content %}
<div class="messaging-container">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h2><i class="fas fa-comments"></i> My Messages</h2>
                <p class="text-muted">Communicate with our support team</p>
            </div>
        </div>
    </div>

    {% if conversations %}
    <div class="row">
        {% for conversation in conversations %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card conversation-card h-100 hover-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">{{ conversation.subject|truncatechars:30 }}</h6>
                    {% if conversation.has_unread_messages %}
                    <span class="badge bg-danger">New</span>
                    {% endif %}
                </div>
                <div class="card-body">
                    <div class="conversation-preview">
                        {% if conversation.last_message %}
                        <p class="text-muted small mb-2">
                            <strong>{{ conversation.last_message.sender.first_name|default:conversation.last_message.sender.username }}:</strong>
                            {{ conversation.last_message.content|truncatechars:50 }}
                        </p>
                        <small class="text-muted">
                            <i class="fas fa-clock"></i> {{ conversation.last_message.created_at|timesince }} ago
                        </small>
                        {% else %}
                        <p class="text-muted">No messages yet</p>
                        {% endif %}
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'messaging:conversation_detail' conversation.pk %}" class="btn btn-primary btn-sm w-100 hover-btn">
                        <i class="fas fa-eye"></i> View Conversation
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="empty-state">
                <div class="card text-center">
                    <div class="card-body py-5">
                        <i class="fas fa-comments fa-5x text-muted mb-4"></i>
                        <h4>No conversations yet</h4>
                        <p class="text-muted">Start a conversation with our support team for any assistance you need.</p>
                        <a href="{% url 'messaging:start_conversation' %}" class="btn btn-success btn-lg hover-btn">
                            <i class="fas fa-plus"></i> Start New Conversation
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if conversations %}
    <div class="row mt-4">
        <div class="col-12 text-center">
            <a href="{% url 'messaging:start_conversation' %}" class="btn btn-success hover-btn">
                <i class="fas fa-plus"></i> Start New Conversation
            </a>
        </div>
    </div>
    {% endif %}
</div>

<style>
.messaging-container {
    background: linear-gradient(135deg, rgba(240, 255, 240, 0.8), rgba(144, 238, 144, 0.1));
    min-height: 70vh;
    padding: 20px;
    border-radius: 15px;
}

.page-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.conversation-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.95);
}

.hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(50, 205, 50, 0.3);
}

.hover-btn {
    transition: all 0.3s ease;
    border-radius: 25px;
}

.hover-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(50, 205, 50, 0.4);
}

.empty-state .card {
    background: rgba(255, 255, 255, 0.95);
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.conversation-preview {
    min-height: 60px;
}
</style>
{% endblock %}
