import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Card,
  Title,
  Paragraph,
  Button,
  Avatar,
  Chip,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAuth } from '../context/AuthContext';
import { apiService } from '../services/apiService';
import { theme, styles } from '../theme/theme';

export default function HomeScreen({ navigation }) {
  const { user } = useAuth();
  const [bookings, setBookings] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      const [bookingsResponse, notificationsResponse] = await Promise.all([
        apiService.getBookings(),
        apiService.getNotifications(),
      ]);
      
      setBookings(bookingsResponse.data.results?.slice(0, 3) || []);
      setNotifications(notificationsResponse.data.results?.slice(0, 5) || []);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed':
        return theme.colors.success;
      case 'pending':
        return theme.colors.warning;
      case 'cancelled':
        return theme.colors.error;
      default:
        return theme.colors.placeholder;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={loading} onRefresh={loadDashboardData} />
      }>
      
      {/* Welcome Section */}
      <Card style={styles.card}>
        <Card.Content>
          <View style={styles.row}>
            <View style={{ flex: 1 }}>
              <Title>Welcome back, {user?.first_name || user?.username}!</Title>
              <Paragraph>Manage your bookings and explore our services</Paragraph>
            </View>
            <Avatar.Icon
              size={50}
              icon="account"
              style={{ backgroundColor: theme.colors.primary }}
            />
          </View>
        </Card.Content>
      </Card>

      {/* Quick Actions */}
      <Card style={styles.card}>
        <Card.Content>
          <Title style={styles.subtitle}>Quick Actions</Title>
          <View style={{ flexDirection: 'row', flexWrap: 'wrap', marginTop: 10 }}>
            <TouchableOpacity
              style={[styles.card, { flex: 1, margin: 5, minWidth: '45%' }]}
              onPress={() => navigation.navigate('Rooms')}>
              <View style={[styles.center, { padding: 15 }]}>
                <Icon name="hotel" size={30} color={theme.colors.primary} />
                <Text style={[styles.text, { marginTop: 5, textAlign: 'center' }]}>
                  Browse Rooms
                </Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.card, { flex: 1, margin: 5, minWidth: '45%' }]}
              onPress={() => navigation.navigate('Bookings')}>
              <View style={[styles.center, { padding: 15 }]}>
                <Icon name="event" size={30} color={theme.colors.primary} />
                <Text style={[styles.text, { marginTop: 5, textAlign: 'center' }]}>
                  My Bookings
                </Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.card, { flex: 1, margin: 5, minWidth: '45%' }]}
              onPress={() => navigation.navigate('Messages')}>
              <View style={[styles.center, { padding: 15 }]}>
                <Icon name="message" size={30} color={theme.colors.primary} />
                <Text style={[styles.text, { marginTop: 5, textAlign: 'center' }]}>
                  Support
                </Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.card, { flex: 1, margin: 5, minWidth: '45%' }]}
              onPress={() => navigation.navigate('Reviews')}>
              <View style={[styles.center, { padding: 15 }]}>
                <Icon name="star" size={30} color={theme.colors.primary} />
                <Text style={[styles.text, { marginTop: 5, textAlign: 'center' }]}>
                  Reviews
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </Card.Content>
      </Card>

      {/* Recent Bookings */}
      <Card style={styles.card}>
        <Card.Content>
          <View style={styles.row}>
            <Title style={styles.subtitle}>Recent Bookings</Title>
            <Button
              mode="text"
              onPress={() => navigation.navigate('Bookings')}
              compact>
              View All
            </Button>
          </View>
          
          {bookings.length > 0 ? (
            bookings.map((booking) => (
              <TouchableOpacity
                key={booking.id}
                onPress={() => navigation.navigate('BookingDetail', { bookingId: booking.id })}>
                <Card style={[styles.card, { marginVertical: 5 }]}>
                  <Card.Content>
                    <View style={styles.row}>
                      <View style={{ flex: 1 }}>
                        <Text style={styles.subtitle}>{booking.room_details?.name}</Text>
                        <Text style={styles.smallText}>
                          {formatDate(booking.check_in_date)} - {formatDate(booking.check_out_date)}
                        </Text>
                        <Text style={styles.smallText}>
                          Reference: {booking.booking_reference}
                        </Text>
                      </View>
                      <Chip
                        style={{ backgroundColor: getStatusColor(booking.status) }}
                        textStyle={{ color: '#fff' }}>
                        {booking.status}
                      </Chip>
                    </View>
                  </Card.Content>
                </Card>
              </TouchableOpacity>
            ))
          ) : (
            <View style={[styles.center, { padding: 20 }]}>
              <Icon name="event-busy" size={50} color={theme.colors.placeholder} />
              <Text style={[styles.text, { marginTop: 10, textAlign: 'center' }]}>
                No bookings yet
              </Text>
              <Button
                mode="contained"
                onPress={() => navigation.navigate('Rooms')}
                style={{ marginTop: 10 }}>
                Browse Rooms
              </Button>
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Notifications */}
      {notifications.length > 0 && (
        <Card style={styles.card}>
          <Card.Content>
            <Title style={styles.subtitle}>Recent Notifications</Title>
            {notifications.map((notification) => (
              <Card key={notification.id} style={[styles.card, { marginVertical: 5 }]}>
                <Card.Content>
                  <Text style={styles.subtitle}>{notification.title}</Text>
                  <Text style={styles.text}>{notification.message}</Text>
                  <Text style={styles.smallText}>
                    {formatDate(notification.created_at)}
                  </Text>
                </Card.Content>
              </Card>
            ))}
          </Card.Content>
        </Card>
      )}

      {/* Guest Services */}
      <Card style={[styles.card, { marginBottom: 20 }]}>
        <Card.Content>
          <Title style={styles.subtitle}>Guest Services</Title>
          <View style={{ marginTop: 10 }}>
            <View style={[styles.row, { marginBottom: 8 }]}>
              <Icon name="wifi" size={20} color={theme.colors.success} />
              <Text style={[styles.text, { marginLeft: 10 }]}>Free WiFi</Text>
            </View>
            <View style={[styles.row, { marginBottom: 8 }]}>
              <Icon name="local-parking" size={20} color={theme.colors.success} />
              <Text style={[styles.text, { marginLeft: 10 }]}>Free Parking</Text>
            </View>
            <View style={[styles.row, { marginBottom: 8 }]}>
              <Icon name="pool" size={20} color={theme.colors.success} />
              <Text style={[styles.text, { marginLeft: 10 }]}>Swimming Pool</Text>
            </View>
            <View style={[styles.row, { marginBottom: 8 }]}>
              <Icon name="fitness-center" size={20} color={theme.colors.success} />
              <Text style={[styles.text, { marginLeft: 10 }]}>Fitness Center</Text>
            </View>
            <View style={[styles.row, { marginBottom: 8 }]}>
              <Icon name="restaurant" size={20} color={theme.colors.success} />
              <Text style={[styles.text, { marginLeft: 10 }]}>Restaurant</Text>
            </View>
            <View style={styles.row}>
              <Icon name="room-service" size={20} color={theme.colors.success} />
              <Text style={[styles.text, { marginLeft: 10 }]}>24/7 Concierge</Text>
            </View>
          </View>
        </Card.Content>
      </Card>
    </ScrollView>
  );
}
