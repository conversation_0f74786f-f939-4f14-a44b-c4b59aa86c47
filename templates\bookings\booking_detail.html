{% extends 'base.html' %}

{% block title %}Booking {{ booking.booking_reference }} - Carthage Hill Guest House{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4><i class="fas fa-calendar-check"></i> Booking Details</h4>
                <span class="badge bg-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% elif booking.status == 'cancelled' %}danger{% else %}secondary{% endif %} fs-6">
                    {{ booking.get_status_display }}
                </span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Booking Information</h5>
                        <p><strong>Reference:</strong> {{ booking.booking_reference }}</p>
                        <p><strong>Room:</strong> {{ booking.room.name }}</p>
                        <p><strong>Room Type:</strong> {{ booking.room.get_room_type_display }}</p>
                        <p><strong>Guests:</strong> {{ booking.guests_count }}</p>
                        <p><strong>Total Price:</strong> ${{ booking.total_price }}</p>
                    </div>
                    <div class="col-md-6">
                        <h5>Dates</h5>
                        <p><strong>Check-in:</strong> {{ booking.check_in_date|date:"F d, Y" }}</p>
                        <p><strong>Check-out:</strong> {{ booking.check_out_date|date:"F d, Y" }}</p>
                        <p><strong>Duration:</strong> {{ booking.get_duration_nights }} night{{ booking.get_duration_nights|pluralize }}</p>
                        <p><strong>Booked on:</strong> {{ booking.created_at|date:"F d, Y" }}</p>
                    </div>
                </div>
                
                {% if booking.special_requests %}
                <div class="mt-3">
                    <h5>Special Requests</h5>
                    <p>{{ booking.special_requests }}</p>
                </div>
                {% endif %}
                
                {% if booking.confirmed_by and booking.confirmed_at %}
                <div class="mt-3">
                    <h5>Confirmation Details</h5>
                    <p><strong>Confirmed by:</strong> {{ booking.confirmed_by.first_name }} {{ booking.confirmed_by.last_name }}</p>
                    <p><strong>Confirmed on:</strong> {{ booking.confirmed_at|date:"F d, Y g:i A" }}</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Room Details -->
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-bed"></i> Room Details</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if booking.room.room_image %}
                    <div class="col-md-4">
                        <img src="{{ booking.room.room_image.url }}" class="img-fluid rounded" alt="{{ booking.room.name }}">
                    </div>
                    <div class="col-md-8">
                    {% else %}
                    <div class="col-12">
                    {% endif %}
                        <h6>{{ booking.room.name }}</h6>
                        <p>{{ booking.room.description }}</p>
                        <p><strong>Capacity:</strong> {{ booking.room.capacity }} guest{{ booking.room.capacity|pluralize }}</p>
                        <p><strong>Price per night:</strong> ${{ booking.room.price_per_night }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-cogs"></i> Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if booking.can_be_modified %}
                    <a href="{% url 'bookings:modify_booking' booking.id %}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Modify Booking
                    </a>
                    {% endif %}
                    
                    {% if booking.can_be_cancelled %}
                    <a href="{% url 'bookings:cancel_booking' booking.id %}" class="btn btn-danger">
                        <i class="fas fa-times"></i> Cancel Booking
                    </a>
                    {% endif %}
                    
                    {% if booking.status == 'completed' and not booking.review %}
                    <a href="{% url 'reviews:create_review' booking.id %}" class="btn btn-success">
                        <i class="fas fa-star"></i> Write Review
                    </a>
                    {% endif %}
                    
                    <a href="{% url 'messaging:start_conversation' %}" class="btn btn-info">
                        <i class="fas fa-comments"></i> Contact Support
                    </a>
                    
                    <a href="{% url 'bookings:my_bookings' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Bookings
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Booking Status</h5>
            </div>
            <div class="card-body">
                {% if booking.status == 'pending' %}
                <div class="alert alert-warning">
                    <i class="fas fa-clock"></i> Your booking is pending approval. You will be notified once it's confirmed.
                </div>
                {% elif booking.status == 'confirmed' %}
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> Your booking is confirmed! We look forward to welcoming you.
                </div>
                {% elif booking.status == 'cancelled' %}
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle"></i> This booking has been cancelled.
                </div>
                {% elif booking.status == 'completed' %}
                <div class="alert alert-info">
                    <i class="fas fa-flag-checkered"></i> This booking has been completed. Thank you for staying with us!
                </div>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-phone"></i> Contact Information</h5>
            </div>
            <div class="card-body">
                <p><i class="fas fa-phone"></i> +216 XX XXX XXX</p>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>
                <p><i class="fas fa-map-marker-alt"></i> Carthage, Tunisia</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
