{% extends 'base.html' %}

{% block title %}Client Dashboard - Carthage Hill Guest House{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-tachometer-alt"></i> Welcome back, {{ user.first_name|default:user.username }}!</h2>
        <p class="text-muted">Manage your bookings and explore our services</p>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-calendar-check fa-3x text-primary mb-3"></i>
                <h5>{{ recent_bookings|length }}</h5>
                <p class="text-muted">Recent Bookings</p>
                <a href="{% url 'bookings:my_bookings' %}" class="btn btn-outline-primary btn-sm">View All</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-bell fa-3x text-warning mb-3"></i>
                <h5>{{ unread_notifications }}</h5>
                <p class="text-muted">Notifications</p>
                <a href="{% url 'messaging:notifications' %}" class="btn btn-outline-warning btn-sm">Check</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-bed fa-3x text-success mb-3"></i>
                <h5>Book Now</h5>
                <p class="text-muted">Find Rooms</p>
                <a href="{% url 'bookings:room_list' %}" class="btn btn-success btn-sm">Browse Rooms</a>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-comments fa-3x text-info mb-3"></i>
                <h5>Support</h5>
                <p class="text-muted">Get Help</p>
                <a href="{% url 'messaging:conversations' %}" class="btn btn-outline-info btn-sm">Contact Us</a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-history"></i> Recent Bookings</h5>
            </div>
            <div class="card-body">
                {% if recent_bookings %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Reference</th>
                                    <th>Room</th>
                                    <th>Check-in</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for booking in recent_bookings %}
                                <tr>
                                    <td><strong>{{ booking.booking_reference }}</strong></td>
                                    <td>{{ booking.room.name }}</td>
                                    <td>{{ booking.check_in_date }}</td>
                                    <td>
                                        <span class="badge bg-{% if booking.status == 'confirmed' %}success{% elif booking.status == 'pending' %}warning{% elif booking.status == 'cancelled' %}danger{% else %}secondary{% endif %}">
                                            {{ booking.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{% url 'bookings:booking_detail' booking.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5>No bookings yet</h5>
                        <p class="text-muted">Start by browsing our available rooms</p>
                        <a href="{% url 'bookings:room_list' %}" class="btn btn-primary">Browse Rooms</a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'bookings:room_list' %}" class="btn btn-primary">
                        <i class="fas fa-search"></i> Find Available Rooms
                    </a>
                    <a href="{% url 'bookings:my_bookings' %}" class="btn btn-outline-primary">
                        <i class="fas fa-list"></i> View All Bookings
                    </a>
                    <a href="{% url 'accounts:profile' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-user-edit"></i> Update Profile
                    </a>
                    <a href="{% url 'messaging:conversations' %}" class="btn btn-outline-info">
                        <i class="fas fa-envelope"></i> Contact Support
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-star"></i> Guest Services</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2"><i class="fas fa-wifi text-success"></i> Free WiFi</li>
                    <li class="mb-2"><i class="fas fa-car text-success"></i> Free Parking</li>
                    <li class="mb-2"><i class="fas fa-swimming-pool text-success"></i> Swimming Pool</li>
                    <li class="mb-2"><i class="fas fa-dumbbell text-success"></i> Fitness Center</li>
                    <li class="mb-2"><i class="fas fa-utensils text-success"></i> Restaurant</li>
                    <li class="mb-2"><i class="fas fa-concierge-bell text-success"></i> 24/7 Concierge</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
