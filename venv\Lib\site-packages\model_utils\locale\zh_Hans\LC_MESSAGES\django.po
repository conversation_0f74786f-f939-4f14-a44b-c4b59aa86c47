# This file is distributed under the same license as the django-model-utils package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2018.
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-04-01 15:01+0200\n"
"PO-Revision-Date: 2018-10-23 15:26+0800\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: models.py:24
msgid "created"
msgstr "创建时间"

#: models.py:25
msgid "modified"
msgstr "修改时间"

#: models.py:49
msgid "start"
msgstr "开始时间"

#: models.py:50
msgid "end"
msgstr "结束时间"

#: models.py:65
msgid "status"
msgstr "状态"

#: models.py:66
msgid "status changed"
msgstr "状态修改时间"
