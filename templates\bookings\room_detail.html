{% extends 'base.html' %}
{% load booking_extras %}

{% block title %}{{ room.name }} - Carthage Hill Guest House{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            {% if room.room_image %}
            <img src="{{ room.room_image.url }}" class="card-img-top" alt="{{ room.name }}" style="height: 400px; object-fit: cover;">
            {% else %}
            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 400px;">
                <i class="fas fa-bed fa-5x text-muted"></i>
            </div>
            {% endif %}
            
            <div class="card-body">
                <h2 class="card-title">{{ room.name }}</h2>
                <div class="mb-3">
                    <span class="badge bg-primary me-2">{{ room.get_room_type_display }}</span>
                    <span class="badge bg-info me-2">{{ room.capacity }} Guest{{ room.capacity|pluralize }}</span>
                    {% if room.is_available %}
                    <span class="badge bg-success">Available</span>
                    {% else %}
                    <span class="badge bg-danger">Unavailable</span>
                    {% endif %}
                </div>
                
                <p class="card-text">{{ room.description }}</p>
                
                <h5>Amenities</h5>
                <div class="mb-3">
                    {% for amenity in room.amenities|split:"," %}
                    <span class="badge bg-outline-secondary me-1">{{ amenity|strip }}</span>
                    {% endfor %}
                </div>
                
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="text-success mb-0">${{ room.price_per_night }}/night</h3>
                </div>
            </div>
        </div>
        
        <!-- Reviews Section -->
        {% if reviews %}
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-star"></i> Recent Reviews</h5>
            </div>
            <div class="card-body">
                {% for review in reviews %}
                <div class="mb-3 pb-3 border-bottom">
                    <div class="d-flex justify-content-between">
                        <strong>{{ review.user.first_name|default:review.user.username }}</strong>
                        <div>
                            {% for i in "12345" %}
                                {% if forloop.counter <= review.overall_rating %}
                                    <i class="fas fa-star text-warning"></i>
                                {% else %}
                                    <i class="far fa-star text-muted"></i>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    <h6>{{ review.title }}</h6>
                    <p>{{ review.comment }}</p>
                    <small class="text-muted">{{ review.created_at|date:"F d, Y" }}</small>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-md-4">
        {% if user.is_authenticated and room.is_available %}
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-calendar-check"></i> Book This Room</h5>
            </div>
            <div class="card-body">
                <form method="post" action="{% url 'bookings:create_booking' room.id %}">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ booking_form.check_in_date.id_for_label }}" class="form-label">Check-in Date</label>
                        {{ booking_form.check_in_date }}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ booking_form.check_out_date.id_for_label }}" class="form-label">Check-out Date</label>
                        {{ booking_form.check_out_date }}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ booking_form.guests_count.id_for_label }}" class="form-label">Number of Guests</label>
                        {{ booking_form.guests_count }}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ booking_form.special_requests.id_for_label }}" class="form-label">Special Requests</label>
                        {{ booking_form.special_requests }}
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-calendar-plus"></i> Book Now
                        </button>
                    </div>
                </form>
            </div>
        </div>
        {% elif not user.is_authenticated %}
        <div class="card">
            <div class="card-body text-center">
                <h5>Want to book this room?</h5>
                <p>Please log in to make a reservation</p>
                <a href="{% url 'accounts:login' %}" class="btn btn-primary">Login</a>
                <a href="{% url 'accounts:register' %}" class="btn btn-outline-primary">Register</a>
            </div>
        </div>
        {% endif %}
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Room Information</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-bed text-primary"></i> <strong>Type:</strong> {{ room.get_room_type_display }}</li>
                    <li><i class="fas fa-users text-primary"></i> <strong>Capacity:</strong> {{ room.capacity }} guest{{ room.capacity|pluralize }}</li>
                    <li><i class="fas fa-dollar-sign text-primary"></i> <strong>Price:</strong> ${{ room.price_per_night }} per night</li>
                    <li><i class="fas fa-check-circle text-success"></i> <strong>Status:</strong> 
                        {% if room.is_available %}Available{% else %}Unavailable{% endif %}
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
