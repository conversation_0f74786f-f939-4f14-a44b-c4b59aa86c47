{% extends 'base.html' %}

{% block title %}Notifications - Carthage Hill Guest House{% endblock %}

{% block content %}
<div class="notifications-container">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h2><i class="fas fa-bell"></i> Notifications</h2>
                <p class="text-muted">Stay updated with your booking status and important messages</p>
                {% if notifications %}
                <div class="notification-actions">
                    <a href="{% url 'messaging:mark_all_notifications_read' %}" class="btn btn-outline-success hover-btn">
                        <i class="fas fa-check-double"></i> Mark All as Read
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    {% if notifications %}
    <div class="row">
        <div class="col-12">
            <div class="notifications-list">
                {% for notification in notifications %}
                <div class="notification-item {% if not notification.is_read %}unread{% endif %} hover-card">
                    <div class="notification-content">
                        <div class="notification-header">
                            <div class="notification-icon">
                                {% if notification.notification_type == 'booking_confirmed' %}
                                    <i class="fas fa-check-circle text-success"></i>
                                {% elif notification.notification_type == 'booking_cancelled' %}
                                    <i class="fas fa-times-circle text-danger"></i>
                                {% elif notification.notification_type == 'booking_created' %}
                                    <i class="fas fa-calendar-plus text-info"></i>
                                {% elif notification.notification_type == 'new_message' %}
                                    <i class="fas fa-envelope text-primary"></i>
                                {% else %}
                                    <i class="fas fa-info-circle text-secondary"></i>
                                {% endif %}
                            </div>
                            <div class="notification-meta">
                                <h6 class="notification-title">{{ notification.title }}</h6>
                                <small class="notification-time">
                                    <i class="fas fa-clock"></i> {{ notification.created_at|timesince }} ago
                                </small>
                            </div>
                            {% if not notification.is_read %}
                            <div class="notification-badge">
                                <span class="badge bg-danger">New</span>
                            </div>
                            {% endif %}
                        </div>
                        <div class="notification-body">
                            <p>{{ notification.message }}</p>
                            {% if notification.related_booking %}
                            <div class="notification-action">
                                <a href="{% url 'bookings:booking_detail' notification.related_booking.id %}" class="btn btn-sm btn-outline-primary hover-btn">
                                    <i class="fas fa-eye"></i> View Booking
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% if not notification.is_read %}
                    <div class="notification-actions">
                        <a href="{% url 'messaging:mark_notification_read' notification.id %}" class="btn btn-sm btn-success hover-btn">
                            <i class="fas fa-check"></i> Mark as Read
                        </a>
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if is_paginated %}
    <nav aria-label="Notifications pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link hover-btn" href="?page=1">First</a>
            </li>
            <li class="page-item">
                <a class="page-link hover-btn" href="?page={{ page_obj.previous_page_number }}">Previous</a>
            </li>
            {% endif %}
            
            <li class="page-item active">
                <span class="page-link">{{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
            </li>
            
            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link hover-btn" href="?page={{ page_obj.next_page_number }}">Next</a>
            </li>
            <li class="page-item">
                <a class="page-link hover-btn" href="?page={{ page_obj.paginator.num_pages }}">Last</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="empty-state">
                <div class="card text-center">
                    <div class="card-body py-5">
                        <i class="fas fa-bell-slash fa-5x text-muted mb-4"></i>
                        <h4>No notifications</h4>
                        <p class="text-muted">You're all caught up! We'll notify you when there are updates about your bookings.</p>
                        <a href="{% url 'bookings:room_list' %}" class="btn btn-primary btn-lg hover-btn">
                            <i class="fas fa-search"></i> Browse Rooms
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
.notifications-container {
    background: linear-gradient(135deg, rgba(240, 255, 240, 0.8), rgba(144, 238, 144, 0.1));
    min-height: 70vh;
    padding: 20px;
    border-radius: 15px;
}

.page-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.notification-actions {
    margin-top: 15px;
}

.notifications-list {
    max-width: 800px;
    margin: 0 auto;
}

.notification-item {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 4px solid #90EE90;
}

.notification-item.unread {
    border-left-color: #32CD32;
    background: rgba(50, 205, 50, 0.05);
}

.hover-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(50, 205, 50, 0.2);
}

.notification-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.notification-icon {
    font-size: 1.5rem;
    margin-right: 15px;
}

.notification-meta {
    flex-grow: 1;
}

.notification-title {
    margin: 0;
    color: #2d5a2d;
}

.notification-time {
    color: #666;
}

.notification-badge {
    margin-left: auto;
}

.notification-body {
    margin-left: 45px;
}

.notification-action {
    margin-top: 10px;
}

.hover-btn {
    transition: all 0.3s ease;
    border-radius: 25px;
}

.hover-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(50, 205, 50, 0.4);
}

.empty-state .card {
    background: rgba(255, 255, 255, 0.95);
    border: none;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.pagination .page-link {
    border-radius: 25px;
    margin: 0 2px;
    border: none;
    background: rgba(255, 255, 255, 0.9);
    color: #2d5a2d;
}

.pagination .page-item.active .page-link {
    background: #32CD32;
    border-color: #32CD32;
}
</style>
{% endblock %}
